# Copyright The OpenTelemetry Authors
# SPDX-License-Identifier: Apache-2.0
# This file is generated by 'make generate-kubernetes-manifests'
---
apiVersion: v1
kind: Namespace
metadata:
  name: otel-demo
---
# Source: opentelemetry-demo/charts/opensearch/templates/poddisruptionbudget.yaml
apiVersion: policy/v1
kind: PodDisruptionBudget
metadata:
  name: "opensearch-pdb"
  labels:
    app.kubernetes.io/name: opensearch
    app.kubernetes.io/instance: opentelemetry-demo
    app.kubernetes.io/version: "2.19.0"
    app.kubernetes.io/component: opensearch
spec:
  maxUnavailable: 1
  selector:
    matchLabels:
      app.kubernetes.io/name: opensearch
      app.kubernetes.io/instance: opentelemetry-demo
---
# Source: opentelemetry-demo/charts/grafana/templates/serviceaccount.yaml
apiVersion: v1
kind: ServiceAccount
automountServiceAccountToken: false
metadata:
  labels:
    app.kubernetes.io/name: grafana
    app.kubernetes.io/instance: opentelemetry-demo
    app.kubernetes.io/version: "11.5.2"
  name: grafana
  namespace: otel-demo
---
# Source: opentelemetry-demo/charts/jaeger/templates/allinone-sa.yaml
apiVersion: v1
kind: ServiceAccount
metadata:
  name: jaeger
  labels:
    app.kubernetes.io/name: jaeger
    app.kubernetes.io/instance: opentelemetry-demo
    app.kubernetes.io/version: "1.53.0"
    app.kubernetes.io/component: all-in-one
automountServiceAccountToken: true
---
# Source: opentelemetry-demo/charts/opentelemetry-collector/templates/serviceaccount.yaml
apiVersion: v1
kind: ServiceAccount
metadata:
  name: otel-collector
  namespace: otel-demo
  labels:
    app.kubernetes.io/name: opentelemetry-collector
    app.kubernetes.io/instance: opentelemetry-demo
    app.kubernetes.io/version: "0.120.0"
    app.kubernetes.io/component: standalone-collector
---
# Source: opentelemetry-demo/charts/prometheus/templates/serviceaccount.yaml
apiVersion: v1
kind: ServiceAccount
metadata:
  labels:
    app.kubernetes.io/component: server
    app.kubernetes.io/name: prometheus
    app.kubernetes.io/instance: opentelemetry-demo
    app.kubernetes.io/version: v3.1.0
    app.kubernetes.io/part-of: prometheus
  name: prometheus
  namespace: otel-demo
  annotations:
    {}
---
# Source: opentelemetry-demo/templates/serviceaccount.yaml
apiVersion: v1
kind: ServiceAccount
metadata:
  name: opentelemetry-demo
  labels:
    
    app.kubernetes.io/instance: opentelemetry-demo
    app.kubernetes.io/version: "2.0.2"
    app.kubernetes.io/part-of: opentelemetry-demo
---
# Source: opentelemetry-demo/charts/grafana/templates/secret.yaml
apiVersion: v1
kind: Secret
metadata:
  name: grafana
  namespace: otel-demo
  labels:
    app.kubernetes.io/name: grafana
    app.kubernetes.io/instance: opentelemetry-demo
    app.kubernetes.io/version: "11.5.2"
type: Opaque
data:
  
  admin-user: "YWRtaW4="
  admin-password: "YWRtaW4="
  ldap-toml: ""
---
# Source: opentelemetry-demo/charts/grafana/templates/configmap.yaml
apiVersion: v1
kind: ConfigMap
metadata:
  name: grafana
  namespace: otel-demo
  labels:
    app.kubernetes.io/name: grafana
    app.kubernetes.io/instance: opentelemetry-demo
    app.kubernetes.io/version: "11.5.2"
data:
  
  plugins: grafana-opensearch-datasource
  grafana.ini: |
    [analytics]
    check_for_updates = true
    [auth]
    disable_login_form = true
    [auth.anonymous]
    enabled = true
    org_name = Main Org.
    org_role = Admin
    [grafana_net]
    url = https://grafana.net
    [log]
    mode = console
    [paths]
    data = /var/lib/grafana/
    logs = /var/log/grafana
    plugins = /var/lib/grafana/plugins
    provisioning = /etc/grafana/provisioning
    [server]
    domain = ''
    root_url = %(protocol)s://%(domain)s:%(http_port)s/grafana
    serve_from_sub_path = true
  datasources.yaml: |
    apiVersion: 1
    datasources:
    - editable: true
      isDefault: true
      jsonData:
        exemplarTraceIdDestinations:
        - datasourceUid: webstore-traces
          name: trace_id
        - name: trace_id
          url: http://localhost:8080/jaeger/ui/trace/$${__value.raw}
          urlDisplayLabel: View in Jaeger UI
      name: Prometheus
      type: prometheus
      uid: webstore-metrics
      url: http://prometheus:9090
    - editable: true
      isDefault: false
      name: Jaeger
      type: jaeger
      uid: webstore-traces
      url: http://jaeger-query:16686/jaeger/ui
    - access: proxy
      editable: true
      isDefault: false
      jsonData:
        database: otel
        flavor: opensearch
        logLevelField: severity.text.keyword
        logMessageField: body
        pplEnabled: true
        timeField: observedTimestamp
        version: 2.18.0
      name: OpenSearch
      type: grafana-opensearch-datasource
      uid: webstore-logs
      url: http://opensearch:9200/
  dashboardproviders.yaml: |
    apiVersion: 1
    providers:
    - disableDeletion: false
      editable: true
      folder: ""
      name: default
      options:
        path: /var/lib/grafana/dashboards/default
      orgId: 1
      type: file
---
# Source: opentelemetry-demo/charts/opensearch/templates/configmap.yaml
apiVersion: v1
kind: ConfigMap
metadata:
  name: opensearch-config
  labels:
    app.kubernetes.io/name: opensearch
    app.kubernetes.io/instance: opentelemetry-demo
    app.kubernetes.io/version: "2.19.0"
    app.kubernetes.io/component: opensearch
data:
  opensearch.yml: |
    cluster.name: opensearch-cluster
    
    # Bind to all interfaces because we don't know what IP address Docker will assign to us.
    network.host: 0.0.0.0
    
    # Setting network.host to a non-loopback address enables the annoying bootstrap checks. "Single-node" mode disables them again.
    # Implicitly done if ".singleNode" is set to "true".
    # discovery.type: single-node
    
    # Start OpenSearch Security Demo Configuration
    # WARNING: revise all the lines below before you go into production
    # plugins:
    #   security:
    #     ssl:
    #       transport:
    #         pemcert_filepath: esnode.pem
    #         pemkey_filepath: esnode-key.pem
    #         pemtrustedcas_filepath: root-ca.pem
    #         enforce_hostname_verification: false
    #       http:
    #         enabled: true
    #         pemcert_filepath: esnode.pem
    #         pemkey_filepath: esnode-key.pem
    #         pemtrustedcas_filepath: root-ca.pem
    #     allow_unsafe_democertificates: true
    #     allow_default_init_securityindex: true
    #     authcz:
    #       admin_dn:
    #         - CN=kirk,OU=client,O=client,L=test,C=de
    #     audit.type: internal_opensearch
    #     enable_snapshot_restore_privilege: true
    #     check_snapshot_restore_write_privileges: true
    #     restapi:
    #       roles_enabled: ["all_access", "security_rest_api_access"]
    #     system_indices:
    #       enabled: true
    #       indices:
    #         [
    #           ".opendistro-alerting-config",
    #           ".opendistro-alerting-alert*",
    #           ".opendistro-anomaly-results*",
    #           ".opendistro-anomaly-detector*",
    #           ".opendistro-anomaly-checkpoints",
    #           ".opendistro-anomaly-detection-state",
    #           ".opendistro-reports-*",
    #           ".opendistro-notifications-*",
    #           ".opendistro-notebooks",
    #           ".opendistro-asynchronous-search-response*",
    #         ]
    ######## End OpenSearch Security Demo Configuration ########
---
# Source: opentelemetry-demo/charts/opentelemetry-collector/templates/configmap.yaml
apiVersion: v1
kind: ConfigMap
metadata:
  name: otel-collector
  namespace: otel-demo
  labels:
    app.kubernetes.io/name: opentelemetry-collector
    app.kubernetes.io/instance: opentelemetry-demo
    app.kubernetes.io/version: "0.120.0"
    app.kubernetes.io/component: standalone-collector
data:
  relay: |
    connectors:
      spanmetrics: {}
    exporters:
      debug: {}
      opensearch:
        http:
          endpoint: http://opensearch:9200
          tls:
            insecure: true
        logs_index: otel
      otlp:
        endpoint: jaeger-collector:4317
        tls:
          insecure: true
      otlphttp/prometheus:
        endpoint: http://prometheus:9090/api/v1/otlp
        tls:
          insecure: true
    extensions:
      health_check:
        endpoint: ${env:MY_POD_IP}:13133
    processors:
      batch: {}
      k8sattributes:
        extract:
          metadata:
          - k8s.namespace.name
          - k8s.deployment.name
          - k8s.statefulset.name
          - k8s.daemonset.name
          - k8s.cronjob.name
          - k8s.job.name
          - k8s.node.name
          - k8s.pod.name
          - k8s.pod.uid
          - k8s.pod.start_time
        passthrough: false
        pod_association:
        - sources:
          - from: resource_attribute
            name: k8s.pod.ip
        - sources:
          - from: resource_attribute
            name: k8s.pod.uid
        - sources:
          - from: connection
      memory_limiter:
        check_interval: 5s
        limit_percentage: 80
        spike_limit_percentage: 25
      resource:
        attributes:
        - action: insert
          from_attribute: k8s.pod.uid
          key: service.instance.id
      transform:
        error_mode: ignore
        trace_statements:
        - context: span
          statements:
          - replace_pattern(name, "\\?.*", "")
          - replace_match(name, "GET /api/products/*", "GET /api/products/{productId}")
    receivers:
      httpcheck/frontend-proxy:
        targets:
        - endpoint: http://frontend-proxy:8080
      jaeger:
        protocols:
          grpc:
            endpoint: ${env:MY_POD_IP}:14250
          thrift_compact:
            endpoint: ${env:MY_POD_IP}:6831
          thrift_http:
            endpoint: ${env:MY_POD_IP}:14268
      otlp:
        protocols:
          grpc:
            endpoint: ${env:MY_POD_IP}:4317
          http:
            cors:
              allowed_origins:
              - http://*
              - https://*
            endpoint: ${env:MY_POD_IP}:4318
      prometheus:
        config:
          scrape_configs:
          - job_name: opentelemetry-collector
            scrape_interval: 10s
            static_configs:
            - targets:
              - ${env:MY_POD_IP}:8888
      redis:
        collection_interval: 10s
        endpoint: valkey-cart:6379
      zipkin:
        endpoint: ${env:MY_POD_IP}:9411
    service:
      extensions:
      - health_check
      pipelines:
        logs:
          exporters:
          - opensearch
          - debug
          processors:
          - k8sattributes
          - memory_limiter
          - resource
          - batch
          receivers:
          - otlp
        metrics:
          exporters:
          - otlphttp/prometheus
          - debug
          processors:
          - k8sattributes
          - memory_limiter
          - resource
          - batch
          receivers:
          - httpcheck/frontend-proxy
          - redis
          - otlp
          - spanmetrics
        traces:
          exporters:
          - otlp
          - debug
          - spanmetrics
          processors:
          - k8sattributes
          - memory_limiter
          - resource
          - transform
          - batch
          receivers:
          - otlp
          - jaeger
          - zipkin
      telemetry:
        metrics:
          address: ${env:MY_POD_IP}:8888
          level: detailed
          readers:
          - periodic:
              exporter:
                otlp:
                  endpoint: otel-collector:4318
                  protocol: grpc
              interval: 10000
              timeout: 5000
---
# Source: opentelemetry-demo/charts/prometheus/templates/cm.yaml
apiVersion: v1
kind: ConfigMap
metadata:
  labels:
    app.kubernetes.io/component: server
    app.kubernetes.io/name: prometheus
    app.kubernetes.io/instance: opentelemetry-demo
    app.kubernetes.io/version: v3.1.0
    app.kubernetes.io/part-of: prometheus
  name: prometheus
  namespace: otel-demo
data:
  allow-snippet-annotations: "false"
  alerting_rules.yml: |
    {}
  alerts: |
    {}
  prometheus.yml: |
    global:
      evaluation_interval: 30s
      scrape_interval: 5s
      scrape_timeout: 3s
    storage:
      tsdb:
        out_of_order_time_window: 30m
    rule_files:
    - /etc/config/recording_rules.yml
    - /etc/config/alerting_rules.yml
    - /etc/config/rules
    - /etc/config/alerts
    scrape_configs:
    - job_name: prometheus
      static_configs:
      - targets:
        - localhost:9090
    - bearer_token_file: /var/run/secrets/kubernetes.io/serviceaccount/token
      job_name: kubernetes-apiservers
      kubernetes_sd_configs:
      - role: endpoints
      relabel_configs:
      - action: keep
        regex: default;kubernetes;https
        source_labels:
        - __meta_kubernetes_namespace
        - __meta_kubernetes_service_name
        - __meta_kubernetes_endpoint_port_name
      scheme: https
      tls_config:
        ca_file: /var/run/secrets/kubernetes.io/serviceaccount/ca.crt
    - bearer_token_file: /var/run/secrets/kubernetes.io/serviceaccount/token
      job_name: kubernetes-nodes
      kubernetes_sd_configs:
      - role: node
      relabel_configs:
      - action: labelmap
        regex: __meta_kubernetes_node_label_(.+)
      - replacement: kubernetes.default.svc:443
        target_label: __address__
      - regex: (.+)
        replacement: /api/v1/nodes/$1/proxy/metrics
        source_labels:
        - __meta_kubernetes_node_name
        target_label: __metrics_path__
      scheme: https
      tls_config:
        ca_file: /var/run/secrets/kubernetes.io/serviceaccount/ca.crt
    - bearer_token_file: /var/run/secrets/kubernetes.io/serviceaccount/token
      job_name: kubernetes-nodes-cadvisor
      kubernetes_sd_configs:
      - role: node
      relabel_configs:
      - action: labelmap
        regex: __meta_kubernetes_node_label_(.+)
      - replacement: kubernetes.default.svc:443
        target_label: __address__
      - regex: (.+)
        replacement: /api/v1/nodes/$1/proxy/metrics/cadvisor
        source_labels:
        - __meta_kubernetes_node_name
        target_label: __metrics_path__
      scheme: https
      tls_config:
        ca_file: /var/run/secrets/kubernetes.io/serviceaccount/ca.crt
    - honor_labels: true
      job_name: kubernetes-service-endpoints
      kubernetes_sd_configs:
      - role: endpoints
      relabel_configs:
      - action: keep
        regex: true
        source_labels:
        - __meta_kubernetes_service_annotation_prometheus_io_scrape
      - action: drop
        regex: true
        source_labels:
        - __meta_kubernetes_service_annotation_prometheus_io_scrape_slow
      - action: replace
        regex: (https?)
        source_labels:
        - __meta_kubernetes_service_annotation_prometheus_io_scheme
        target_label: __scheme__
      - action: replace
        regex: (.+)
        source_labels:
        - __meta_kubernetes_service_annotation_prometheus_io_path
        target_label: __metrics_path__
      - action: replace
        regex: (.+?)(?::\d+)?;(\d+)
        replacement: $1:$2
        source_labels:
        - __address__
        - __meta_kubernetes_service_annotation_prometheus_io_port
        target_label: __address__
      - action: labelmap
        regex: __meta_kubernetes_service_annotation_prometheus_io_param_(.+)
        replacement: __param_$1
      - action: labelmap
        regex: __meta_kubernetes_service_label_(.+)
      - action: replace
        source_labels:
        - __meta_kubernetes_namespace
        target_label: namespace
      - action: replace
        source_labels:
        - __meta_kubernetes_service_name
        target_label: service
      - action: replace
        source_labels:
        - __meta_kubernetes_pod_node_name
        target_label: node
    - honor_labels: true
      job_name: kubernetes-service-endpoints-slow
      kubernetes_sd_configs:
      - role: endpoints
      relabel_configs:
      - action: keep
        regex: true
        source_labels:
        - __meta_kubernetes_service_annotation_prometheus_io_scrape_slow
      - action: replace
        regex: (https?)
        source_labels:
        - __meta_kubernetes_service_annotation_prometheus_io_scheme
        target_label: __scheme__
      - action: replace
        regex: (.+)
        source_labels:
        - __meta_kubernetes_service_annotation_prometheus_io_path
        target_label: __metrics_path__
      - action: replace
        regex: (.+?)(?::\d+)?;(\d+)
        replacement: $1:$2
        source_labels:
        - __address__
        - __meta_kubernetes_service_annotation_prometheus_io_port
        target_label: __address__
      - action: labelmap
        regex: __meta_kubernetes_service_annotation_prometheus_io_param_(.+)
        replacement: __param_$1
      - action: labelmap
        regex: __meta_kubernetes_service_label_(.+)
      - action: replace
        source_labels:
        - __meta_kubernetes_namespace
        target_label: namespace
      - action: replace
        source_labels:
        - __meta_kubernetes_service_name
        target_label: service
      - action: replace
        source_labels:
        - __meta_kubernetes_pod_node_name
        target_label: node
      scrape_interval: 5m
      scrape_timeout: 30s
    - honor_labels: true
      job_name: prometheus-pushgateway
      kubernetes_sd_configs:
      - role: service
      relabel_configs:
      - action: keep
        regex: pushgateway
        source_labels:
        - __meta_kubernetes_service_annotation_prometheus_io_probe
    - honor_labels: true
      job_name: kubernetes-services
      kubernetes_sd_configs:
      - role: service
      metrics_path: /probe
      params:
        module:
        - http_2xx
      relabel_configs:
      - action: keep
        regex: true
        source_labels:
        - __meta_kubernetes_service_annotation_prometheus_io_probe
      - source_labels:
        - __address__
        target_label: __param_target
      - replacement: blackbox
        target_label: __address__
      - source_labels:
        - __param_target
        target_label: instance
      - action: labelmap
        regex: __meta_kubernetes_service_label_(.+)
      - source_labels:
        - __meta_kubernetes_namespace
        target_label: namespace
      - source_labels:
        - __meta_kubernetes_service_name
        target_label: service
    - honor_labels: true
      job_name: kubernetes-pods
      kubernetes_sd_configs:
      - role: pod
      relabel_configs:
      - action: keep
        regex: true
        source_labels:
        - __meta_kubernetes_pod_annotation_prometheus_io_scrape
      - action: drop
        regex: true
        source_labels:
        - __meta_kubernetes_pod_annotation_prometheus_io_scrape_slow
      - action: replace
        regex: (https?)
        source_labels:
        - __meta_kubernetes_pod_annotation_prometheus_io_scheme
        target_label: __scheme__
      - action: replace
        regex: (.+)
        source_labels:
        - __meta_kubernetes_pod_annotation_prometheus_io_path
        target_label: __metrics_path__
      - action: replace
        regex: (\d+);(([A-Fa-f0-9]{1,4}::?){1,7}[A-Fa-f0-9]{1,4})
        replacement: '[$2]:$1'
        source_labels:
        - __meta_kubernetes_pod_annotation_prometheus_io_port
        - __meta_kubernetes_pod_ip
        target_label: __address__
      - action: replace
        regex: (\d+);((([0-9]+?)(\.|$)){4})
        replacement: $2:$1
        source_labels:
        - __meta_kubernetes_pod_annotation_prometheus_io_port
        - __meta_kubernetes_pod_ip
        target_label: __address__
      - action: labelmap
        regex: __meta_kubernetes_pod_annotation_prometheus_io_param_(.+)
        replacement: __param_$1
      - action: labelmap
        regex: __meta_kubernetes_pod_label_(.+)
      - action: replace
        source_labels:
        - __meta_kubernetes_namespace
        target_label: namespace
      - action: replace
        source_labels:
        - __meta_kubernetes_pod_name
        target_label: pod
      - action: drop
        regex: Pending|Succeeded|Failed|Completed
        source_labels:
        - __meta_kubernetes_pod_phase
      - action: replace
        source_labels:
        - __meta_kubernetes_pod_node_name
        target_label: node
    - honor_labels: true
      job_name: kubernetes-pods-slow
      kubernetes_sd_configs:
      - role: pod
      relabel_configs:
      - action: keep
        regex: true
        source_labels:
        - __meta_kubernetes_pod_annotation_prometheus_io_scrape_slow
      - action: replace
        regex: (https?)
        source_labels:
        - __meta_kubernetes_pod_annotation_prometheus_io_scheme
        target_label: __scheme__
      - action: replace
        regex: (.+)
        source_labels:
        - __meta_kubernetes_pod_annotation_prometheus_io_path
        target_label: __metrics_path__
      - action: replace
        regex: (\d+);(([A-Fa-f0-9]{1,4}::?){1,7}[A-Fa-f0-9]{1,4})
        replacement: '[$2]:$1'
        source_labels:
        - __meta_kubernetes_pod_annotation_prometheus_io_port
        - __meta_kubernetes_pod_ip
        target_label: __address__
      - action: replace
        regex: (\d+);((([0-9]+?)(\.|$)){4})
        replacement: $2:$1
        source_labels:
        - __meta_kubernetes_pod_annotation_prometheus_io_port
        - __meta_kubernetes_pod_ip
        target_label: __address__
      - action: labelmap
        regex: __meta_kubernetes_pod_annotation_prometheus_io_param_(.+)
        replacement: __param_$1
      - action: labelmap
        regex: __meta_kubernetes_pod_label_(.+)
      - action: replace
        source_labels:
        - __meta_kubernetes_namespace
        target_label: namespace
      - action: replace
        source_labels:
        - __meta_kubernetes_pod_name
        target_label: pod
      - action: drop
        regex: Pending|Succeeded|Failed|Completed
        source_labels:
        - __meta_kubernetes_pod_phase
      - action: replace
        source_labels:
        - __meta_kubernetes_pod_node_name
        target_label: node
      scrape_interval: 5m
      scrape_timeout: 30s
  recording_rules.yml: |
    {}
  rules: |
    {}
---
# Source: opentelemetry-demo/templates/flagd-config.yaml
apiVersion: v1
kind: ConfigMap
metadata:
  name: flagd-config
  namespace: otel-demo
  labels:
    
    app.kubernetes.io/instance: opentelemetry-demo
    app.kubernetes.io/version: "2.0.2"
    app.kubernetes.io/part-of: opentelemetry-demo
data:
  
  demo.flagd.json: |
    {
      "$schema": "https://flagd.dev/schema/v0/flags.json",
      "flags": {
        "productCatalogFailure": {
          "description": "Fail product catalog service on a specific product",
          "state": "ENABLED",
          "variants": {
            "on": true,
            "off": false
          },
          "defaultVariant": "off"
        },
        "recommendationCacheFailure": {
          "description": "Fail recommendation service cache",
          "state": "ENABLED",
          "variants": {
            "on": true,
            "off": false
          },
          "defaultVariant": "off"
        },
        "adManualGc": {
          "description": "Triggers full manual garbage collections in the ad service",
          "state": "ENABLED",
          "variants": {
            "on": true,
            "off": false
          },
          "defaultVariant": "off"
        },
        "adHighCpu": {
          "description": "Triggers high cpu load in the ad service",
          "state": "ENABLED",
          "variants": {
            "on": true,
            "off": false
          },
          "defaultVariant": "off"
        },
        "adFailure": {
          "description": "Fail ad service",
          "state": "ENABLED",
          "variants": {
            "on": true,
            "off": false
          },
          "defaultVariant": "off"
        },
        "kafkaQueueProblems": {
          "description": "Overloads Kafka queue while simultaneously introducing a consumer side delay leading to a lag spike",
          "state": "ENABLED",
          "variants": {
            "on": 100,
            "off": 0
          },
          "defaultVariant": "off"
        },
        "cartFailure": {
          "description": "Fail cart service",
          "state": "ENABLED",
          "variants": {
            "on": true,
            "off": false
          },
          "defaultVariant": "off"
        },
        "paymentFailure": {
          "description": "Fail payment service charge requests n%",
          "state": "ENABLED",
          "variants": {
            "100%": 1,
            "90%": 0.95,
            "75%": 0.75,
            "50%": 0.5,
            "25%": 0.25,
            "10%": 0.1,
            "off": 0
          },
          "defaultVariant": "off"
        },
        "paymentUnreachable": {
          "description": "Payment service is unavailable",
          "state": "ENABLED",
          "variants": {
            "on": true,
            "off": false
          },
          "defaultVariant": "off"
        },
        "loadGeneratorFloodHomepage": {
          "description": "Flood the frontend with a large amount of requests.",
          "state": "ENABLED",
          "variants": {
            "on": 100,
            "off": 0
          },
          "defaultVariant": "off"
        },
        "imageSlowLoad": {
          "description": "slow loading images in the frontend",
          "state": "ENABLED",
          "variants": {
            "10sec": 10000,
            "5sec": 5000,
            "off": 0
          },
          "defaultVariant": "off"
        }
      }
    }
---
# Source: opentelemetry-demo/templates/grafana-dashboards.yaml
apiVersion: v1
kind: ConfigMap
metadata:
  name: grafana-dashboards
  namespace: otel-demo
  labels:
    
    app.kubernetes.io/instance: opentelemetry-demo
    app.kubernetes.io/version: "2.0.2"
    app.kubernetes.io/part-of: opentelemetry-demo
data:
  
  demo-dashboard.json: |-
    {
      "annotations": {
        "list": [
          {
            "builtIn": 1,
            "datasource": {
              "type": "grafana",
              "uid": "-- Grafana --"
            },
            "enable": true,
            "hide": true,
            "iconColor": "rgba(0, 211, 255, 1)",
            "name": "Annotations & Alerts",
            "target": {
              "limit": 100,
              "matchAny": false,
              "tags": [],
              "type": "dashboard"
            },
            "type": "dashboard"
          }
        ]
      },
      "editable": true,
      "fiscalYearStartMonth": 0,
      "graphTooltip": 1,
      "id": 2,
      "links": [],
      "panels": [
        {
          "fieldConfig": {
            "defaults": {},
            "overrides": []
          },
          "gridPos": {
            "h": 3,
            "w": 24,
            "x": 0,
            "y": 0
          },
          "id": 21,
          "options": {
            "code": {
              "language": "plaintext",
              "showLineNumbers": false,
              "showMiniMap": false
            },
            "content": "This dashboard shows RED metrics for the selected service, as generated by the spanmetrics connector in the OpenTelemetry Collector.\nIf the selected service emits logs, the logs will also be displayed.\nCustom metrics generated by some services are also displayed. \n<br/>\nChart panels may require 5 minutes after the Demo is started before rendering data.",
            "mode": "html"
          },
          "pluginVersion": "11.5.2",
          "title": "",
          "type": "text"
        },
        {
          "collapsed": false,
          "gridPos": {
            "h": 1,
            "w": 24,
            "x": 0,
            "y": 3
          },
          "id": 14,
          "panels": [],
          "title": "Spanmetrics (RED metrics)",
          "type": "row"
        },
        {
          "datasource": {
            "type": "prometheus",
            "uid": "webstore-metrics"
          },
          "fieldConfig": {
            "defaults": {
              "color": {
                "mode": "palette-classic"
              },
              "custom": {
                "axisBorderShow": false,
                "axisCenteredZero": false,
                "axisColorMode": "text",
                "axisLabel": "",
                "axisPlacement": "auto",
                "barAlignment": 0,
                "barWidthFactor": 0.6,
                "drawStyle": "line",
                "fillOpacity": 0,
                "gradientMode": "none",
                "hideFrom": {
                  "legend": false,
                  "tooltip": false,
                  "viz": false
                },
                "insertNulls": false,
                "lineInterpolation": "linear",
                "lineWidth": 1,
                "pointSize": 5,
                "scaleDistribution": {
                  "type": "linear"
                },
                "showPoints": "never",
                "spanNulls": false,
                "stacking": {
                  "group": "A",
                  "mode": "none"
                },
                "thresholdsStyle": {
                  "mode": "off"
                }
              },
              "mappings": [],
              "thresholds": {
                "mode": "absolute",
                "steps": [
                  {
                    "color": "green",
                    "value": null
                  },
                  {
                    "color": "red",
                    "value": 80
                  }
                ]
              },
              "unit": "reqps"
            },
            "overrides": []
          },
          "gridPos": {
            "h": 8,
            "w": 8,
            "x": 0,
            "y": 4
          },
          "id": 12,
          "interval": "2m",
          "options": {
            "legend": {
              "calcs": [],
              "displayMode": "list",
              "placement": "bottom",
              "showLegend": true
            },
            "tooltip": {
              "hideZeros": false,
              "mode": "multi",
              "sort": "desc"
            }
          },
          "pluginVersion": "11.5.2",
          "targets": [
            {
              "datasource": {
                "type": "prometheus",
                "uid": "webstore-metrics"
              },
              "editorMode": "code",
              "expr": "sum by (span_name) (rate(traces_span_metrics_duration_milliseconds_count{service_name=\"${service}\"}[$__rate_interval]))",
              "legendFormat": "{{ span_name }}",
              "range": true,
              "refId": "A"
            }
          ],
          "title": "Requests Rate by Span Name",
          "type": "timeseries"
        },
        {
          "datasource": {
            "type": "prometheus",
            "uid": "webstore-metrics"
          },
          "description": "",
          "fieldConfig": {
            "defaults": {
              "color": {
                "mode": "palette-classic"
              },
              "custom": {
                "axisBorderShow": false,
                "axisCenteredZero": false,
                "axisColorMode": "text",
                "axisLabel": "",
                "axisPlacement": "auto",
                "barAlignment": 0,
                "barWidthFactor": 0.6,
                "drawStyle": "line",
                "fillOpacity": 0,
                "gradientMode": "none",
                "hideFrom": {
                  "legend": false,
                  "tooltip": false,
                  "viz": false
                },
                "insertNulls": false,
                "lineInterpolation": "linear",
                "lineWidth": 1,
                "pointSize": 5,
                "scaleDistribution": {
                  "type": "linear"
                },
                "showPoints": "never",
                "spanNulls": false,
                "stacking": {
                  "group": "A",
                  "mode": "none"
                },
                "thresholdsStyle": {
                  "mode": "off"
                }
              },
              "mappings": [],
              "thresholds": {
                "mode": "absolute",
                "steps": [
                  {
                    "color": "green",
                    "value": null
                  },
                  {
                    "color": "red",
                    "value": 80
                  }
                ]
              }
            },
            "overrides": []
          },
          "gridPos": {
            "h": 8,
            "w": 8,
            "x": 8,
            "y": 4
          },
          "id": 10,
          "interval": "2m",
          "options": {
            "legend": {
              "calcs": [],
              "displayMode": "list",
              "placement": "bottom",
              "showLegend": true
            },
            "tooltip": {
              "hideZeros": false,
              "mode": "multi",
              "sort": "desc"
            }
          },
          "pluginVersion": "11.5.2",
          "targets": [
            {
              "datasource": {
                "type": "prometheus",
                "uid": "webstore-metrics"
              },
              "editorMode": "code",
              "expr": "sum by (span_name) (rate(traces_span_metrics_calls_total{status_code=\"STATUS_CODE_ERROR\", service_name=\"${service}\"}[$__rate_interval]))",
              "interval": "",
              "legendFormat": "{{ span_name }}",
              "range": true,
              "refId": "A"
            }
          ],
          "title": "Error Rate by Span Name",
          "type": "timeseries"
        },
        {
          "datasource": {
            "type": "prometheus",
            "uid": "webstore-metrics"
          },
          "description": "",
          "fieldConfig": {
            "defaults": {
              "color": {
                "mode": "palette-classic"
              },
              "custom": {
                "axisBorderShow": false,
                "axisCenteredZero": false,
                "axisColorMode": "text",
                "axisLabel": "",
                "axisPlacement": "auto",
                "barAlignment": 0,
                "barWidthFactor": 0.6,
                "drawStyle": "line",
                "fillOpacity": 0,
                "gradientMode": "none",
                "hideFrom": {
                  "legend": false,
                  "tooltip": false,
                  "viz": false
                },
                "insertNulls": false,
                "lineInterpolation": "linear",
                "lineWidth": 1,
                "pointSize": 5,
                "scaleDistribution": {
                  "type": "linear"
                },
                "showPoints": "never",
                "spanNulls": false,
                "stacking": {
                  "group": "A",
                  "mode": "none"
                },
                "thresholdsStyle": {
                  "mode": "off"
                }
              },
              "mappings": [],
              "thresholds": {
                "mode": "absolute",
                "steps": [
                  {
                    "color": "green",
                    "value": null
                  },
                  {
                    "color": "red",
                    "value": 80
                  }
                ]
              },
              "unit": "dtdurationms"
            },
            "overrides": []
          },
          "gridPos": {
            "h": 8,
            "w": 8,
            "x": 16,
            "y": 4
          },
          "id": 2,
          "interval": "2m",
          "options": {
            "legend": {
              "calcs": [],
              "displayMode": "list",
              "placement": "bottom",
              "showLegend": true
            },
            "tooltip": {
              "hideZeros": false,
              "mode": "multi",
              "sort": "desc"
            }
          },
          "pluginVersion": "11.5.2",
          "targets": [
            {
              "datasource": {
                "type": "prometheus",
                "uid": "webstore-metrics"
              },
              "editorMode": "code",
              "exemplar": false,
              "expr": "histogram_quantile(0.50, sum(rate(traces_span_metrics_duration_milliseconds_bucket{service_name=\"${service}\"}[$__rate_interval])) by (le, span_name))",
              "legendFormat": "{{span_name}}",
              "range": true,
              "refId": "A"
            }
          ],
          "title": "Average Duration by Span Name",
          "type": "timeseries"
        },
        {
          "collapsed": false,
          "gridPos": {
            "h": 1,
            "w": 24,
            "x": 0,
            "y": 12
          },
          "id": 19,
          "panels": [],
          "title": "Application Log Records",
          "type": "row"
        },
        {
          "datasource": {
            "type": "grafana-opensearch-datasource",
            "uid": "webstore-logs"
          },
          "description": "",
          "fieldConfig": {
            "defaults": {
              "color": {
                "mode": "thresholds"
              },
              "custom": {
                "align": "left",
                "cellOptions": {
                  "type": "auto"
                },
                "inspect": false
              },
              "mappings": [],
              "thresholds": {
                "mode": "absolute",
                "steps": [
                  {
                    "color": "green",
                    "value": null
                  },
                  {
                    "color": "red",
                    "value": 80
                  }
                ]
              }
            },
            "overrides": [
              {
                "matcher": {
                  "id": "byName",
                  "options": "count()"
                },
                "properties": [
                  {
                    "id": "custom.width",
                    "value": 90
                  }
                ]
              }
            ]
          },
          "gridPos": {
            "h": 8,
            "w": 4,
            "x": 0,
            "y": 13
          },
          "id": 20,
          "options": {
            "cellHeight": "sm",
            "footer": {
              "countRows": false,
              "fields": "",
              "reducer": [
                "sum"
              ],
              "show": false
            },
            "showHeader": true
          },
          "pluginVersion": "11.5.2",
          "targets": [
            {
              "alias": "",
              "bucketAggs": [
                {
                  "field": "severity.text.keyword",
                  "id": "3",
                  "settings": {
                    "min_doc_count": "1",
                    "order": "desc",
                    "orderBy": "_count",
                    "size": "0"
                  },
                  "type": "terms"
                }
              ],
              "datasource": {
                "type": "grafana-opensearch-datasource",
                "uid": "webstore-logs"
              },
              "format": "table",
              "metrics": [
                {
                  "id": "1",
                  "type": "count"
                }
              ],
              "query": "search source=otel\n| where resource.service.name=\"${service}\"\n| stats count() by severity.text",
              "queryType": "PPL",
              "refId": "A",
              "timeField": "observedTimestamp"
            }
          ],
          "title": "Log Records by Severity",
          "transformations": [
            {
              "id": "organize",
              "options": {
                "excludeByName": {},
                "includeByName": {},
                "indexByName": {},
                "renameByName": {
                  "Count": "",
                  "severity.text.keyword": "Severity"
                }
              }
            }
          ],
          "type": "table"
        },
        {
          "datasource": {
            "type": "grafana-opensearch-datasource",
            "uid": "webstore-logs"
          },
          "fieldConfig": {
            "defaults": {
              "color": {
                "mode": "thresholds"
              },
              "custom": {
                "align": "auto",
                "cellOptions": {
                  "type": "auto",
                  "wrapText": false
                },
                "filterable": true,
                "inspect": true
              },
              "mappings": [],
              "thresholds": {
                "mode": "absolute",
                "steps": [
                  {
                    "color": "green",
                    "value": null
                  },
                  {
                    "color": "red",
                    "value": 80
                  }
                ]
              }
            },
            "overrides": [
              {
                "matcher": {
                  "id": "byName",
                  "options": "observedTimestamp"
                },
                "properties": []
              },
              {
                "matcher": {
                  "id": "byName",
                  "options": "body"
                },
                "properties": [
                  {
                    "id": "custom.width",
                    "value": 386
                  }
                ]
              },
              {
                "matcher": {
                  "id": "byName",
                  "options": "severity.text"
                },
                "properties": [
                  {
                    "id": "custom.width",
                    "value": 127
                  }
                ]
              }
            ]
          },
          "gridPos": {
            "h": 8,
            "w": 20,
            "x": 4,
            "y": 13
          },
          "id": 17,
          "options": {
            "cellHeight": "sm",
            "footer": {
              "countRows": false,
              "fields": "",
              "reducer": [
                "sum"
              ],
              "show": false
            },
            "showHeader": true,
            "sortBy": []
          },
          "pluginVersion": "11.5.2",
          "targets": [
            {
              "alias": "",
              "bucketAggs": [],
              "datasource": {
                "type": "grafana-opensearch-datasource",
                "uid": "webstore-logs"
              },
              "format": "table",
              "hide": false,
              "metrics": [
                {
                  "id": "1",
                  "settings": {
                    "order": "desc",
                    "size": "100",
                    "useTimeRange": true
                  },
                  "type": "raw_data"
                }
              ],
              "query": "search source=otel\n| where resource.service.name=\"${service}\"\n| sort - observedTimestamp \n| head 100",
              "queryType": "PPL",
              "refId": "A",
              "timeField": "observedTimestamp"
            }
          ],
          "title": "Log Records (100 recent entries)",
          "transformations": [
            {
              "id": "organize",
              "options": {
                "excludeByName": {
                  "@timestamp": true
                },
                "includeByName": {},
                "indexByName": {
                  "@timestamp": 1,
                  "attributes.data_stream.dataset": 4,
                  "attributes.data_stream.namespace": 5,
                  "attributes.data_stream.type": 6,
                  "attributes.productId": 7,
                  "attributes.quantity": 8,
                  "attributes.userId": 9,
                  "body": 3,
                  "instrumentationScope.name": 10,
                  "observedTimestamp": 0,
                  "resource.container.id": 11,
                  "resource.docker.cli.cobra.command_path": 12,
                  "resource.host.name": 13,
                  "resource.service.name": 14,
                  "resource.telemetry.sdk.language": 15,
                  "resource.telemetry.sdk.name": 16,
                  "resource.telemetry.sdk.version": 17,
                  "severity.number": 18,
                  "severity.text": 2,
                  "spanId": 19,
                  "traceId": 20
                },
                "renameByName": {}
              }
            }
          ],
          "type": "table"
        },
        {
          "collapsed": false,
          "gridPos": {
            "h": 1,
            "w": 24,
            "x": 0,
            "y": 21
          },
          "id": 18,
          "panels": [],
          "title": "Application Metrics",
          "type": "row"
        },
        {
          "datasource": {
            "type": "prometheus",
            "uid": "webstore-metrics"
          },
          "fieldConfig": {
            "defaults": {
              "color": {
                "mode": "palette-classic"
              },
              "custom": {
                "axisBorderShow": false,
                "axisCenteredZero": false,
                "axisColorMode": "text",
                "axisLabel": "",
                "axisPlacement": "auto",
                "barAlignment": 0,
                "barWidthFactor": 0.6,
                "drawStyle": "line",
                "fillOpacity": 0,
                "gradientMode": "none",
                "hideFrom": {
                  "legend": false,
                  "tooltip": false,
                  "viz": false
                },
                "insertNulls": false,
                "lineInterpolation": "linear",
                "lineWidth": 1,
                "pointSize": 5,
                "scaleDistribution": {
                  "type": "linear"
                },
                "showPoints": "never",
                "spanNulls": false,
                "stacking": {
                  "group": "A",
                  "mode": "none"
                },
                "thresholdsStyle": {
                  "mode": "off"
                }
              },
              "mappings": [],
              "thresholds": {
                "mode": "absolute",
                "steps": [
                  {
                    "color": "green",
                    "value": null
                  },
                  {
                    "color": "red",
                    "value": 80
                  }
                ]
              },
              "unit": "percent"
            },
            "overrides": []
          },
          "gridPos": {
            "h": 8,
            "w": 12,
            "x": 0,
            "y": 22
          },
          "id": 6,
          "options": {
            "legend": {
              "calcs": [],
              "displayMode": "list",
              "placement": "bottom",
              "showLegend": true
            },
            "tooltip": {
              "hideZeros": false,
              "mode": "multi",
              "sort": "desc"
            }
          },
          "pluginVersion": "11.5.2",
          "targets": [
            {
              "datasource": {
                "type": "prometheus",
                "uid": "webstore-metrics"
              },
              "editorMode": "code",
              "expr": "rate(process_runtime_cpython_cpu_time_seconds_total{type=~\"system\"}[$__rate_interval])*100",
              "hide": false,
              "interval": "2m",
              "legendFormat": "{{job}} ({{type}})",
              "range": true,
              "refId": "A"
            },
            {
              "datasource": {
                "type": "prometheus",
                "uid": "webstore-metrics"
              },
              "editorMode": "code",
              "expr": "rate(process_runtime_cpython_cpu_time_seconds_total{type=~\"user\"}[$__rate_interval])*100",
              "hide": false,
              "interval": "2m",
              "legendFormat": "{{job}} ({{type}})",
              "range": true,
              "refId": "B"
            }
          ],
          "title": "Python services (CPU%)",
          "transformations": [
            {
              "id": "renameByRegex",
              "options": {
                "regex": "opentelemetry-demo/(.*)",
                "renamePattern": "$1"
              }
            }
          ],
          "type": "timeseries"
        },
        {
          "datasource": {
            "type": "prometheus",
            "uid": "webstore-metrics"
          },
          "fieldConfig": {
            "defaults": {
              "color": {
                "mode": "palette-classic"
              },
              "custom": {
                "axisBorderShow": false,
                "axisCenteredZero": false,
                "axisColorMode": "text",
                "axisLabel": "",
                "axisPlacement": "auto",
                "barAlignment": 0,
                "barWidthFactor": 0.6,
                "drawStyle": "line",
                "fillOpacity": 0,
                "gradientMode": "none",
                "hideFrom": {
                  "legend": false,
                  "tooltip": false,
                  "viz": false
                },
                "insertNulls": false,
                "lineInterpolation": "linear",
                "lineWidth": 1,
                "pointSize": 5,
                "scaleDistribution": {
                  "type": "linear"
                },
                "showPoints": "never",
                "spanNulls": false,
                "stacking": {
                  "group": "A",
                  "mode": "none"
                },
                "thresholdsStyle": {
                  "mode": "off"
                }
              },
              "mappings": [],
              "thresholds": {
                "mode": "absolute",
                "steps": [
                  {
                    "color": "green",
                    "value": null
                  },
                  {
                    "color": "red",
                    "value": 80
                  }
                ]
              },
              "unit": "bytes"
            },
            "overrides": []
          },
          "gridPos": {
            "h": 8,
            "w": 12,
            "x": 12,
            "y": 22
          },
          "id": 8,
          "options": {
            "legend": {
              "calcs": [],
              "displayMode": "list",
              "placement": "bottom",
              "showLegend": true
            },
            "tooltip": {
              "hideZeros": false,
              "mode": "multi",
              "sort": "desc"
            }
          },
          "pluginVersion": "11.5.2",
          "targets": [
            {
              "datasource": {
                "type": "prometheus",
                "uid": "webstore-metrics"
              },
              "editorMode": "code",
              "expr": "process_runtime_cpython_memory_bytes{type=\"rss\"}",
              "legendFormat": "{{job}}",
              "range": true,
              "refId": "A"
            }
          ],
          "title": "Python services (Memory)",
          "transformations": [
            {
              "id": "renameByRegex",
              "options": {
                "regex": "opentelemetry-demo/(.*)",
                "renamePattern": "$1"
              }
            }
          ],
          "type": "timeseries"
        },
        {
          "datasource": {
            "type": "prometheus",
            "uid": "webstore-metrics"
          },
          "fieldConfig": {
            "defaults": {
              "color": {
                "mode": "palette-classic"
              },
              "custom": {
                "axisBorderShow": false,
                "axisCenteredZero": false,
                "axisColorMode": "text",
                "axisLabel": "",
                "axisPlacement": "auto",
                "barAlignment": 0,
                "barWidthFactor": 0.6,
                "drawStyle": "line",
                "fillOpacity": 0,
                "gradientMode": "none",
                "hideFrom": {
                  "legend": false,
                  "tooltip": false,
                  "viz": false
                },
                "insertNulls": false,
                "lineInterpolation": "linear",
                "lineWidth": 1,
                "pointSize": 5,
                "scaleDistribution": {
                  "type": "linear"
                },
                "showPoints": "never",
                "spanNulls": false,
                "stacking": {
                  "group": "A",
                  "mode": "none"
                },
                "thresholdsStyle": {
                  "mode": "off"
                }
              },
              "mappings": [],
              "thresholds": {
                "mode": "absolute",
                "steps": [
                  {
                    "color": "green",
                    "value": null
                  },
                  {
                    "color": "red",
                    "value": 80
                  }
                ]
              }
            },
            "overrides": []
          },
          "gridPos": {
            "h": 8,
            "w": 12,
            "x": 0,
            "y": 30
          },
          "id": 4,
          "options": {
            "legend": {
              "calcs": [],
              "displayMode": "list",
              "placement": "bottom",
              "showLegend": false
            },
            "tooltip": {
              "hideZeros": false,
              "mode": "multi",
              "sort": "desc"
            }
          },
          "pluginVersion": "11.5.2",
          "targets": [
            {
              "datasource": {
                "type": "prometheus",
                "uid": "webstore-metrics"
              },
              "editorMode": "code",
              "expr": "rate(app_recommendations_counter_total{recommendation_type=\"catalog\"}[$__rate_interval])",
              "interval": "2m",
              "legendFormat": "recommendations",
              "range": true,
              "refId": "A"
            }
          ],
          "title": "Recommendations Rate",
          "type": "timeseries"
        },
        {
          "datasource": {
            "type": "prometheus",
            "uid": "webstore-metrics"
          },
          "fieldConfig": {
            "defaults": {
              "color": {
                "mode": "palette-classic"
              },
              "custom": {
                "axisBorderShow": false,
                "axisCenteredZero": false,
                "axisColorMode": "text",
                "axisLabel": "",
                "axisPlacement": "auto",
                "barAlignment": 0,
                "barWidthFactor": 0.6,
                "drawStyle": "line",
                "fillOpacity": 0,
                "gradientMode": "none",
                "hideFrom": {
                  "legend": false,
                  "tooltip": false,
                  "viz": false
                },
                "insertNulls": false,
                "lineInterpolation": "linear",
                "lineWidth": 1,
                "pointSize": 5,
                "scaleDistribution": {
                  "type": "linear"
                },
                "showPoints": "never",
                "spanNulls": false,
                "stacking": {
                  "group": "A",
                  "mode": "none"
                },
                "thresholdsStyle": {
                  "mode": "off"
                }
              },
              "mappings": [],
              "thresholds": {
                "mode": "absolute",
                "steps": [
                  {
                    "color": "green",
                    "value": null
                  },
                  {
                    "color": "red",
                    "value": 80
                  }
                ]
              }
            },
            "overrides": []
          },
          "gridPos": {
            "h": 8,
            "w": 12,
            "x": 12,
            "y": 30
          },
          "id": 16,
          "interval": "2m",
          "options": {
            "legend": {
              "calcs": [],
              "displayMode": "list",
              "placement": "bottom",
              "showLegend": true
            },
            "tooltip": {
              "hideZeros": false,
              "mode": "multi",
              "sort": "desc"
            }
          },
          "pluginVersion": "11.5.2",
          "targets": [
            {
              "datasource": {
                "type": "prometheus",
                "uid": "webstore-metrics"
              },
              "editorMode": "code",
              "expr": "rate(otel_trace_span_processor_spans{job=\"quote\"}[2m])*120",
              "interval": "2m",
              "legendFormat": "{{state}}",
              "range": true,
              "refId": "A"
            }
          ],
          "title": "Quote Service batch span processor",
          "type": "timeseries"
        },
        {
          "collapsed": false,
          "gridPos": {
            "h": 1,
            "w": 24,
            "x": 0,
            "y": 38
          },
          "id": 23,
          "panels": [],
          "title": "Service Dependency",
          "type": "row"
        },
        {
          "datasource": {
            "type": "jaeger",
            "uid": "webstore-traces"
          },
          "fieldConfig": {
            "defaults": {},
            "overrides": []
          },
          "gridPos": {
            "h": 18,
            "w": 24,
            "x": 0,
            "y": 39
          },
          "id": 22,
          "options": {
            "edges": {
              "mainStatUnit": "requests"
            },
            "nodes": {
              "arcs": [],
              "mainStatUnit": ""
            },
            "zoomMode": "cooperative"
          },
          "pluginVersion": "11.5.2",
          "targets": [
            {
              "datasource": {
                "type": "jaeger",
                "uid": "webstore-traces"
              },
              "queryType": "dependencyGraph",
              "refId": "A"
            }
          ],
          "title": "Service Dependency",
          "type": "nodeGraph"
        }
      ],
      "preload": false,
      "refresh": "",
      "schemaVersion": 40,
      "tags": [],
      "templating": {
        "list": [
          {
            "current": {
              "text": "frontend",
              "value": "frontend"
            },
            "datasource": {
              "type": "prometheus",
              "uid": "webstore-metrics"
            },
            "definition": "traces_span_metrics_duration_milliseconds_bucket",
            "includeAll": false,
            "label": "Service",
            "name": "service",
            "options": [],
            "query": {
              "query": "traces_span_metrics_duration_milliseconds_bucket",
              "refId": "PrometheusVariableQueryEditor-VariableQuery"
            },
            "refresh": 1,
            "regex": "/.*service.name=\\\"([^\\\"]+)\\\".*/",
            "sort": 1,
            "type": "query"
          }
        ]
      },
      "time": {
        "from": "now-15m",
        "to": "now"
      },
      "timepicker": {},
      "timezone": "",
      "title": "Demo Dashboard",
      "uid": "W2gX2zHVk",
      "version": 2,
      "weekStart": ""
    }
  exemplars-dashboard.json: |-
    {
      "annotations": {
        "list": [
          {
            "builtIn": 1,
            "datasource": {
              "type": "grafana",
              "uid": "-- Grafana --"
            },
            "enable": true,
            "hide": true,
            "iconColor": "rgba(0, 211, 255, 1)",
            "name": "Annotations & Alerts",
            "type": "dashboard"
          }
        ]
      },
      "editable": true,
      "fiscalYearStartMonth": 0,
      "graphTooltip": 0,
      "id": 5,
      "links": [],
      "panels": [
        {
          "fieldConfig": {
            "defaults": {},
            "overrides": []
          },
          "gridPos": {
            "h": 2,
            "w": 24,
            "x": 0,
            "y": 0
          },
          "id": 8,
          "options": {
            "code": {
              "language": "plaintext",
              "showLineNumbers": false,
              "showMiniMap": false
            },
            "content": "This dashboard shows the use of metric exemplars.\nExemplars can be used to look up a trace in Jaeger.\nOnly the most recent exemplars may still be available in Jaeger.\n<br>\nChart panels may require 5 minutes after the Demo is started before rendering data.",
            "mode": "html"
          },
          "pluginVersion": "11.4.0",
          "title": "",
          "type": "text"
        },
        {
          "collapsed": false,
          "gridPos": {
            "h": 1,
            "w": 24,
            "x": 0,
            "y": 2
          },
          "id": 4,
          "panels": [],
          "title": "GetCart Exemplars",
          "type": "row"
        },
        {
          "datasource": {
            "type": "prometheus",
            "uid": "webstore-metrics"
          },
          "fieldConfig": {
            "defaults": {
              "custom": {
                "hideFrom": {
                  "legend": false,
                  "tooltip": false,
                  "viz": false
                },
                "scaleDistribution": {
                  "type": "linear"
                }
              }
            },
            "overrides": []
          },
          "gridPos": {
            "h": 9,
            "w": 24,
            "x": 0,
            "y": 3
          },
          "id": 2,
          "interval": "2m",
          "options": {
            "calculate": false,
            "cellGap": 1,
            "color": {
              "exponent": 0.5,
              "fill": "dark-orange",
              "mode": "scheme",
              "reverse": false,
              "scale": "exponential",
              "scheme": "Spectral",
              "steps": 64
            },
            "exemplars": {
              "color": "rgba(255,0,255,0.7)"
            },
            "filterValues": {
              "le": 1e-9
            },
            "legend": {
              "show": true
            },
            "rowsFrame": {
              "layout": "auto"
            },
            "tooltip": {
              "mode": "single",
              "showColorScale": false,
              "yHistogram": false
            },
            "yAxis": {
              "axisPlacement": "left",
              "reverse": false
            }
          },
          "pluginVersion": "11.4.0",
          "targets": [
            {
              "disableTextWrap": false,
              "editorMode": "builder",
              "exemplar": true,
              "expr": "sum by(le) (rate(app_cart_get_cart_latency_bucket[$__rate_interval]))",
              "format": "heatmap",
              "fullMetaSearch": false,
              "includeNullMetadata": false,
              "instant": true,
              "legendFormat": "{{le}}",
              "range": true,
              "refId": "A",
              "useBackend": false
            }
          ],
          "title": "GetCart Latency Heatmap with Exemplars",
          "type": "heatmap"
        },
        {
          "datasource": {
            "type": "prometheus",
            "uid": "webstore-metrics"
          },
          "fieldConfig": {
            "defaults": {
              "color": {
                "mode": "palette-classic"
              },
              "custom": {
                "axisBorderShow": false,
                "axisCenteredZero": false,
                "axisColorMode": "text",
                "axisLabel": "",
                "axisPlacement": "auto",
                "barAlignment": 0,
                "barWidthFactor": 0.6,
                "drawStyle": "line",
                "fillOpacity": 0,
                "gradientMode": "none",
                "hideFrom": {
                  "legend": false,
                  "tooltip": false,
                  "viz": false
                },
                "insertNulls": false,
                "lineInterpolation": "linear",
                "lineWidth": 1,
                "pointSize": 5,
                "scaleDistribution": {
                  "type": "linear"
                },
                "showPoints": "auto",
                "spanNulls": false,
                "stacking": {
                  "group": "A",
                  "mode": "none"
                },
                "thresholdsStyle": {
                  "mode": "off"
                }
              },
              "mappings": [],
              "thresholds": {
                "mode": "absolute",
                "steps": [
                  {
                    "color": "green",
                    "value": null
                  },
                  {
                    "color": "red",
                    "value": 80
                  }
                ]
              }
            },
            "overrides": []
          },
          "gridPos": {
            "h": 10,
            "w": 24,
            "x": 0,
            "y": 12
          },
          "id": 5,
          "interval": "2m",
          "options": {
            "legend": {
              "calcs": [],
              "displayMode": "list",
              "placement": "bottom",
              "showLegend": true
            },
            "tooltip": {
              "mode": "single",
              "sort": "none"
            }
          },
          "pluginVersion": "11.4.0",
          "targets": [
            {
              "datasource": {
                "type": "prometheus",
                "uid": "webstore-metrics"
              },
              "disableTextWrap": false,
              "editorMode": "builder",
              "exemplar": true,
              "expr": "histogram_quantile(0.95, sum by(le) (rate(app_cart_get_cart_latency_bucket[$__rate_interval])))",
              "fullMetaSearch": false,
              "includeNullMetadata": false,
              "legendFormat": "p95 GetCart",
              "range": true,
              "refId": "A",
              "useBackend": false
            }
          ],
          "title": "95th Pct Cart GetCart Latency with Exemplars",
          "type": "timeseries"
        },
        {
          "collapsed": false,
          "gridPos": {
            "h": 1,
            "w": 24,
            "x": 0,
            "y": 22
          },
          "id": 3,
          "panels": [],
          "title": "AddItem Exemplars",
          "type": "row"
        },
        {
          "datasource": {
            "type": "prometheus",
            "uid": "webstore-metrics"
          },
          "fieldConfig": {
            "defaults": {
              "custom": {
                "hideFrom": {
                  "legend": false,
                  "tooltip": false,
                  "viz": false
                },
                "scaleDistribution": {
                  "type": "linear"
                }
              }
            },
            "overrides": []
          },
          "gridPos": {
            "h": 9,
            "w": 24,
            "x": 0,
            "y": 23
          },
          "id": 6,
          "interval": "2m",
          "options": {
            "calculate": false,
            "cellGap": 1,
            "color": {
              "exponent": 0.5,
              "fill": "dark-orange",
              "mode": "scheme",
              "reverse": false,
              "scale": "exponential",
              "scheme": "Spectral",
              "steps": 64
            },
            "exemplars": {
              "color": "rgba(255,0,255,0.7)"
            },
            "filterValues": {
              "le": 1e-9
            },
            "legend": {
              "show": true
            },
            "rowsFrame": {
              "layout": "auto"
            },
            "tooltip": {
              "mode": "single",
              "showColorScale": false,
              "yHistogram": false
            },
            "yAxis": {
              "axisPlacement": "left",
              "reverse": false
            }
          },
          "pluginVersion": "11.4.0",
          "targets": [
            {
              "disableTextWrap": false,
              "editorMode": "builder",
              "exemplar": true,
              "expr": "sum by(le) (rate(app_cart_add_item_latency_bucket[$__rate_interval]))",
              "format": "heatmap",
              "fullMetaSearch": false,
              "includeNullMetadata": false,
              "instant": true,
              "legendFormat": "{{le}}",
              "range": true,
              "refId": "A",
              "useBackend": false
            }
          ],
          "title": "AddItem Latency Heatmap with Exemplars",
          "type": "heatmap"
        },
        {
          "datasource": {
            "type": "prometheus",
            "uid": "webstore-metrics"
          },
          "fieldConfig": {
            "defaults": {
              "color": {
                "mode": "palette-classic"
              },
              "custom": {
                "axisBorderShow": false,
                "axisCenteredZero": false,
                "axisColorMode": "text",
                "axisLabel": "",
                "axisPlacement": "auto",
                "barAlignment": 0,
                "barWidthFactor": 0.6,
                "drawStyle": "line",
                "fillOpacity": 0,
                "gradientMode": "none",
                "hideFrom": {
                  "legend": false,
                  "tooltip": false,
                  "viz": false
                },
                "insertNulls": false,
                "lineInterpolation": "linear",
                "lineWidth": 1,
                "pointSize": 5,
                "scaleDistribution": {
                  "type": "linear"
                },
                "showPoints": "auto",
                "spanNulls": false,
                "stacking": {
                  "group": "A",
                  "mode": "none"
                },
                "thresholdsStyle": {
                  "mode": "off"
                }
              },
              "mappings": [],
              "thresholds": {
                "mode": "absolute",
                "steps": [
                  {
                    "color": "green"
                  },
                  {
                    "color": "red",
                    "value": 80
                  }
                ]
              }
            },
            "overrides": []
          },
          "gridPos": {
            "h": 10,
            "w": 24,
            "x": 0,
            "y": 32
          },
          "id": 1,
          "interval": "2m",
          "options": {
            "legend": {
              "calcs": [],
              "displayMode": "list",
              "placement": "bottom",
              "showLegend": true
            },
            "tooltip": {
              "mode": "single",
              "sort": "none"
            }
          },
          "pluginVersion": "11.4.0",
          "targets": [
            {
              "datasource": {
                "type": "prometheus",
                "uid": "webstore-metrics"
              },
              "disableTextWrap": false,
              "editorMode": "builder",
              "exemplar": true,
              "expr": "histogram_quantile(0.95, sum by(le) (rate(app_cart_add_item_latency_bucket[$__rate_interval])))",
              "fullMetaSearch": false,
              "includeNullMetadata": false,
              "legendFormat": "p95 AddItem",
              "range": true,
              "refId": "A",
              "useBackend": false
            }
          ],
          "title": "95th Pct Cart AddItem Latency with Exemplars",
          "type": "timeseries"
        }
      ],
      "preload": false,
      "schemaVersion": 40,
      "tags": [],
      "templating": {
        "list": []
      },
      "time": {
        "from": "now-15m",
        "to": "now"
      },
      "timepicker": {},
      "timezone": "browser",
      "title": "Cart Service Exemplars",
      "uid": "ce6sd46kfkglca",
      "version": 3,
      "weekStart": ""
    }
  opentelemetry-collector.json: |-
    {
      "annotations": {
        "list": [
          {
            "builtIn": 1,
            "datasource": {
              "type": "datasource",
              "uid": "grafana"
            },
            "enable": true,
            "hide": true,
            "iconColor": "rgba(0, 211, 255, 1)",
            "name": "Annotations & Alerts",
            "target": {
              "limit": 100,
              "matchAny": false,
              "tags": [],
              "type": "dashboard"
            },
            "type": "dashboard"
          }
        ]
      },
      "description": "Visualize OpenTelemetry (OTEL) collector metrics (tested with OTEL contrib v0.120.1)",
      "editable": true,
      "fiscalYearStartMonth": 0,
      "graphTooltip": 1,
      "id": 4,
      "links": [],
      "panels": [
        {
          "fieldConfig": {
            "defaults": {},
            "overrides": []
          },
          "gridPos": {
            "h": 2,
            "w": 24,
            "x": 0,
            "y": 0
          },
          "id": 86,
          "options": {
            "code": {
              "language": "plaintext",
              "showLineNumbers": false,
              "showMiniMap": false
            },
            "content": "This dashboard uses the metrics generated by the OpenTelemetry Collector.\nIt is used to understand the overall performance and health of the OpenTelemetry Collector.\n<br/>\nChart panels may require 5 minutes after the Demo is started before rendering data.\n",
            "mode": "html"
          },
          "pluginVersion": "11.5.2",
          "title": "",
          "type": "text"
        },
        {
          "collapsed": false,
          "gridPos": {
            "h": 1,
            "w": 24,
            "x": 0,
            "y": 2
          },
          "id": 23,
          "panels": [],
          "title": "Receivers",
          "type": "row"
        },
        {
          "datasource": {
            "type": "prometheus",
            "uid": "$datasource"
          },
          "description": "Accepted: count/rate of spans successfully pushed into the pipeline.\nRefused: count/rate of spans that could not be pushed into the pipeline.",
          "fieldConfig": {
            "defaults": {
              "color": {
                "mode": "palette-classic"
              },
              "custom": {
                "axisBorderShow": false,
                "axisCenteredZero": false,
                "axisColorMode": "text",
                "axisLabel": "",
                "axisPlacement": "auto",
                "barAlignment": 0,
                "barWidthFactor": 0.6,
                "drawStyle": "line",
                "fillOpacity": 0,
                "gradientMode": "none",
                "hideFrom": {
                  "legend": false,
                  "tooltip": false,
                  "viz": false
                },
                "insertNulls": false,
                "lineInterpolation": "linear",
                "lineWidth": 1,
                "pointSize": 5,
                "scaleDistribution": {
                  "type": "linear"
                },
                "showPoints": "never",
                "spanNulls": true,
                "stacking": {
                  "group": "A",
                  "mode": "none"
                },
                "thresholdsStyle": {
                  "mode": "off"
                }
              },
              "links": [],
              "mappings": [],
              "min": 0,
              "thresholds": {
                "mode": "absolute",
                "steps": [
                  {
                    "color": "green",
                    "value": null
                  },
                  {
                    "color": "red",
                    "value": 80
                  }
                ]
              },
              "unit": "short"
            },
            "overrides": [
              {
                "matcher": {
                  "id": "byRegexp",
                  "options": "/Refused.*/"
                },
                "properties": [
                  {
                    "id": "color",
                    "value": {
                      "fixedColor": "red",
                      "mode": "fixed"
                    }
                  },
                  {
                    "id": "custom.axisPlacement",
                    "value": "right"
                  }
                ]
              }
            ]
          },
          "gridPos": {
            "h": 8,
            "w": 8,
            "x": 0,
            "y": 3
          },
          "id": 28,
          "interval": "$minstep",
          "options": {
            "legend": {
              "calcs": [
                "min",
                "max",
                "mean"
              ],
              "displayMode": "table",
              "placement": "bottom",
              "showLegend": true
            },
            "tooltip": {
              "hideZeros": false,
              "mode": "multi",
              "sort": "none"
            }
          },
          "pluginVersion": "11.5.2",
          "targets": [
            {
              "datasource": {
                "type": "prometheus",
                "uid": "$datasource"
              },
              "editorMode": "code",
              "exemplar": false,
              "expr": "sum(${metric:value}(otelcol_receiver_accepted_spans${suffix_total}{receiver=~\"$receiver\",job=\"$job\"}[$__rate_interval])) by (receiver $grouping)",
              "format": "time_series",
              "interval": "$minstep",
              "intervalFactor": 1,
              "legendFormat": "Accepted: {{receiver}} {{transport}} {{service_instance_id}}",
              "range": true,
              "refId": "A"
            },
            {
              "datasource": {
                "type": "prometheus",
                "uid": "$datasource"
              },
              "editorMode": "code",
              "exemplar": false,
              "expr": "sum(${metric:value}(otelcol_receiver_refused_spans${suffix_total}{receiver=~\"$receiver\",job=\"$job\"}[$__rate_interval])) by (receiver $grouping)",
              "format": "time_series",
              "hide": false,
              "interval": "$minstep",
              "intervalFactor": 1,
              "legendFormat": "Refused: {{receiver}} {{transport}} {{service_instance_id}}",
              "range": true,
              "refId": "B"
            }
          ],
          "title": "Spans ${metric:text}",
          "type": "timeseries"
        },
        {
          "datasource": {
            "type": "prometheus",
            "uid": "$datasource"
          },
          "description": "Accepted: count/rate of metric points successfully pushed into the pipeline.\nRefused: count/rate of metric points that could not be pushed into the pipeline.",
          "fieldConfig": {
            "defaults": {
              "color": {
                "mode": "palette-classic"
              },
              "custom": {
                "axisBorderShow": false,
                "axisCenteredZero": false,
                "axisColorMode": "text",
                "axisLabel": "",
                "axisPlacement": "auto",
                "barAlignment": 0,
                "barWidthFactor": 0.6,
                "drawStyle": "line",
                "fillOpacity": 0,
                "gradientMode": "none",
                "hideFrom": {
                  "legend": false,
                  "tooltip": false,
                  "viz": false
                },
                "insertNulls": false,
                "lineInterpolation": "linear",
                "lineWidth": 1,
                "pointSize": 5,
                "scaleDistribution": {
                  "type": "linear"
                },
                "showPoints": "never",
                "spanNulls": true,
                "stacking": {
                  "group": "A",
                  "mode": "none"
                },
                "thresholdsStyle": {
                  "mode": "off"
                }
              },
              "links": [],
              "mappings": [],
              "min": 0,
              "thresholds": {
                "mode": "absolute",
                "steps": [
                  {
                    "color": "green",
                    "value": null
                  },
                  {
                    "color": "red",
                    "value": 80
                  }
                ]
              },
              "unit": "short"
            },
            "overrides": [
              {
                "matcher": {
                  "id": "byRegexp",
                  "options": "/Refused.*/"
                },
                "properties": [
                  {
                    "id": "color",
                    "value": {
                      "fixedColor": "red",
                      "mode": "fixed"
                    }
                  },
                  {
                    "id": "custom.axisPlacement",
                    "value": "right"
                  }
                ]
              }
            ]
          },
          "gridPos": {
            "h": 8,
            "w": 8,
            "x": 8,
            "y": 3
          },
          "id": 80,
          "interval": "$minstep",
          "options": {
            "legend": {
              "calcs": [
                "min",
                "max",
                "mean"
              ],
              "displayMode": "table",
              "placement": "bottom",
              "showLegend": true
            },
            "tooltip": {
              "hideZeros": false,
              "mode": "multi",
              "sort": "none"
            }
          },
          "pluginVersion": "11.5.2",
          "targets": [
            {
              "datasource": {
                "type": "prometheus",
                "uid": "$datasource"
              },
              "editorMode": "code",
              "exemplar": false,
              "expr": "sum(${metric:value}(otelcol_receiver_accepted_metric_points${suffix_total}{receiver=~\"$receiver\",job=\"$job\"}[$__rate_interval])) by (receiver $grouping)",
              "format": "time_series",
              "interval": "$minstep",
              "intervalFactor": 1,
              "legendFormat": "Accepted: {{receiver}} {{transport}} {{service_instance_id}}",
              "range": true,
              "refId": "A"
            },
            {
              "datasource": {
                "type": "prometheus",
                "uid": "$datasource"
              },
              "editorMode": "code",
              "exemplar": false,
              "expr": "sum(${metric:value}(otelcol_receiver_refused_metric_points${suffix_total}{receiver=~\"$receiver\",job=\"$job\"}[$__rate_interval])) by (receiver $grouping)",
              "format": "time_series",
              "hide": false,
              "interval": "$minstep",
              "intervalFactor": 1,
              "legendFormat": "Refused: {{receiver}} {{transport}} {{service_instance_id}}",
              "range": true,
              "refId": "B"
            }
          ],
          "title": "Metric Points ${metric:text}",
          "type": "timeseries"
        },
        {
          "datasource": {
            "type": "prometheus",
            "uid": "$datasource"
          },
          "description": "Accepted: count/rate of log records successfully pushed into the pipeline.\nRefused: count/rate of log records that could not be pushed into the pipeline.",
          "fieldConfig": {
            "defaults": {
              "color": {
                "mode": "palette-classic"
              },
              "custom": {
                "axisBorderShow": false,
                "axisCenteredZero": false,
                "axisColorMode": "text",
                "axisLabel": "",
                "axisPlacement": "auto",
                "barAlignment": 0,
                "barWidthFactor": 0.6,
                "drawStyle": "line",
                "fillOpacity": 0,
                "gradientMode": "none",
                "hideFrom": {
                  "legend": false,
                  "tooltip": false,
                  "viz": false
                },
                "insertNulls": false,
                "lineInterpolation": "linear",
                "lineWidth": 1,
                "pointSize": 5,
                "scaleDistribution": {
                  "type": "linear"
                },
                "showPoints": "never",
                "spanNulls": true,
                "stacking": {
                  "group": "A",
                  "mode": "none"
                },
                "thresholdsStyle": {
                  "mode": "off"
                }
              },
              "links": [],
              "mappings": [],
              "min": 0,
              "thresholds": {
                "mode": "absolute",
                "steps": [
                  {
                    "color": "green",
                    "value": null
                  },
                  {
                    "color": "red",
                    "value": 80
                  }
                ]
              },
              "unit": "short"
            },
            "overrides": [
              {
                "matcher": {
                  "id": "byRegexp",
                  "options": "/Refused.*/"
                },
                "properties": [
                  {
                    "id": "color",
                    "value": {
                      "fixedColor": "red",
                      "mode": "fixed"
                    }
                  },
                  {
                    "id": "custom.axisPlacement",
                    "value": "right"
                  }
                ]
              }
            ]
          },
          "gridPos": {
            "h": 8,
            "w": 8,
            "x": 16,
            "y": 3
          },
          "id": 47,
          "interval": "$minstep",
          "options": {
            "legend": {
              "calcs": [
                "min",
                "max",
                "mean"
              ],
              "displayMode": "table",
              "placement": "bottom",
              "showLegend": true
            },
            "tooltip": {
              "hideZeros": false,
              "mode": "multi",
              "sort": "none"
            }
          },
          "pluginVersion": "11.5.2",
          "targets": [
            {
              "datasource": {
                "type": "prometheus",
                "uid": "$datasource"
              },
              "editorMode": "code",
              "exemplar": false,
              "expr": "sum(${metric:value}(otelcol_receiver_accepted_log_records${suffix_total}{receiver=~\"$receiver\",job=\"$job\"}[$__rate_interval])) by (receiver $grouping)",
              "format": "time_series",
              "interval": "$minstep",
              "intervalFactor": 1,
              "legendFormat": "Accepted: {{receiver}} {{transport}} {{service_instance_id}}",
              "range": true,
              "refId": "A"
            },
            {
              "datasource": {
                "type": "prometheus",
                "uid": "$datasource"
              },
              "editorMode": "code",
              "exemplar": false,
              "expr": "sum(${metric:value}(otelcol_receiver_refused_log_records${suffix_total}{receiver=~\"$receiver\",job=\"$job\"}[$__rate_interval])) by (receiver $grouping)",
              "format": "time_series",
              "hide": false,
              "interval": "$minstep",
              "intervalFactor": 1,
              "legendFormat": "Refused: {{receiver}} {{transport}} {{service_instance_id}}",
              "range": true,
              "refId": "B"
            }
          ],
          "title": "Log Records ${metric:text}",
          "type": "timeseries"
        },
        {
          "collapsed": false,
          "gridPos": {
            "h": 1,
            "w": 24,
            "x": 0,
            "y": 11
          },
          "id": 34,
          "panels": [],
          "title": "Processors",
          "type": "row"
        },
        {
          "datasource": {
            "type": "prometheus",
            "uid": "$datasource"
          },
          "description": "",
          "fieldConfig": {
            "defaults": {
              "color": {
                "mode": "palette-classic"
              },
              "custom": {
                "axisBorderShow": false,
                "axisCenteredZero": false,
                "axisColorMode": "text",
                "axisLabel": "",
                "axisPlacement": "auto",
                "barAlignment": 0,
                "barWidthFactor": 0.6,
                "drawStyle": "line",
                "fillOpacity": 0,
                "gradientMode": "none",
                "hideFrom": {
                  "legend": false,
                  "tooltip": false,
                  "viz": false
                },
                "insertNulls": false,
                "lineInterpolation": "linear",
                "lineWidth": 1,
                "pointSize": 5,
                "scaleDistribution": {
                  "type": "linear"
                },
                "showPoints": "never",
                "spanNulls": true,
                "stacking": {
                  "group": "A",
                  "mode": "none"
                },
                "thresholdsStyle": {
                  "mode": "off"
                }
              },
              "links": [],
              "mappings": [],
              "thresholds": {
                "mode": "absolute",
                "steps": [
                  {
                    "color": "green",
                    "value": null
                  },
                  {
                    "color": "red",
                    "value": 80
                  }
                ]
              },
              "unit": "short"
            },
            "overrides": [
              {
                "matcher": {
                  "id": "byRegexp",
                  "options": "/Refused.*/"
                },
                "properties": [
                  {
                    "id": "color",
                    "value": {
                      "fixedColor": "red",
                      "mode": "fixed"
                    }
                  },
                  {
                    "id": "custom.axisPlacement",
                    "value": "right"
                  }
                ]
              },
              {
                "matcher": {
                  "id": "byRegexp",
                  "options": "/Dropped.*/"
                },
                "properties": [
                  {
                    "id": "color",
                    "value": {
                      "fixedColor": "purple",
                      "mode": "fixed"
                    }
                  },
                  {
                    "id": "custom.axisPlacement",
                    "value": "right"
                  }
                ]
              }
            ]
          },
          "gridPos": {
            "h": 8,
            "w": 8,
            "x": 0,
            "y": 12
          },
          "id": 85,
          "interval": "$minstep",
          "options": {
            "legend": {
              "calcs": [
                "min",
                "max",
                "mean"
              ],
              "displayMode": "table",
              "placement": "bottom",
              "showLegend": true
            },
            "tooltip": {
              "hideZeros": false,
              "mode": "multi",
              "sort": "desc"
            }
          },
          "pluginVersion": "11.5.2",
          "targets": [
            {
              "datasource": {
                "type": "prometheus",
                "uid": "$datasource"
              },
              "editorMode": "code",
              "exemplar": false,
              "expr": "sum(${metric:value}(otelcol_processor_incoming_items${suffix_total}{processor=~\"$processor\",job=\"$job\",otel_signal=\"traces\"}[$__rate_interval])) by (processor $grouping)",
              "format": "time_series",
              "interval": "$minstep",
              "intervalFactor": 1,
              "legendFormat": "Incomming: {{processor}} {{service_instance_id}}",
              "range": true,
              "refId": "A"
            },
            {
              "datasource": {
                "type": "prometheus",
                "uid": "$datasource"
              },
              "editorMode": "code",
              "exemplar": false,
              "expr": "0-sum(${metric:value}(otelcol_processor_outgoing_items${suffix_total}{processor=~\"$processor\",job=\"$job\",otel_signal=\"traces\"}[$__rate_interval])) by (processor $grouping)",
              "format": "time_series",
              "hide": false,
              "interval": "$minstep",
              "intervalFactor": 1,
              "legendFormat": "Outgoing: {{processor}} {{service_instance_id}}",
              "range": true,
              "refId": "B"
            }
          ],
          "title": "Spans ${metric:text}",
          "type": "timeseries"
        },
        {
          "datasource": {
            "type": "prometheus",
            "uid": "$datasource"
          },
          "description": "",
          "fieldConfig": {
            "defaults": {
              "color": {
                "mode": "palette-classic"
              },
              "custom": {
                "axisBorderShow": false,
                "axisCenteredZero": false,
                "axisColorMode": "text",
                "axisLabel": "",
                "axisPlacement": "auto",
                "barAlignment": 0,
                "barWidthFactor": 0.6,
                "drawStyle": "line",
                "fillOpacity": 0,
                "gradientMode": "none",
                "hideFrom": {
                  "legend": false,
                  "tooltip": false,
                  "viz": false
                },
                "insertNulls": false,
                "lineInterpolation": "linear",
                "lineWidth": 1,
                "pointSize": 5,
                "scaleDistribution": {
                  "type": "linear"
                },
                "showPoints": "never",
                "spanNulls": true,
                "stacking": {
                  "group": "A",
                  "mode": "none"
                },
                "thresholdsStyle": {
                  "mode": "off"
                }
              },
              "links": [],
              "mappings": [],
              "thresholds": {
                "mode": "absolute",
                "steps": [
                  {
                    "color": "green",
                    "value": null
                  },
                  {
                    "color": "red",
                    "value": 80
                  }
                ]
              },
              "unit": "short"
            },
            "overrides": [
              {
                "matcher": {
                  "id": "byRegexp",
                  "options": "/Refused.*/"
                },
                "properties": [
                  {
                    "id": "color",
                    "value": {
                      "fixedColor": "red",
                      "mode": "fixed"
                    }
                  },
                  {
                    "id": "custom.axisPlacement",
                    "value": "right"
                  }
                ]
              },
              {
                "matcher": {
                  "id": "byRegexp",
                  "options": "/Dropped.*/"
                },
                "properties": [
                  {
                    "id": "color",
                    "value": {
                      "fixedColor": "purple",
                      "mode": "fixed"
                    }
                  },
                  {
                    "id": "custom.axisPlacement",
                    "value": "right"
                  }
                ]
              }
            ]
          },
          "gridPos": {
            "h": 8,
            "w": 8,
            "x": 8,
            "y": 12
          },
          "id": 83,
          "interval": "$minstep",
          "options": {
            "legend": {
              "calcs": [
                "min",
                "max",
                "mean"
              ],
              "displayMode": "table",
              "placement": "bottom",
              "showLegend": true
            },
            "tooltip": {
              "hideZeros": false,
              "mode": "multi",
              "sort": "desc"
            }
          },
          "pluginVersion": "11.5.2",
          "targets": [
            {
              "datasource": {
                "type": "prometheus",
                "uid": "$datasource"
              },
              "editorMode": "code",
              "exemplar": false,
              "expr": "sum(${metric:value}(otelcol_processor_incoming_items${suffix_total}{processor=~\"$processor\",job=\"$job\",otel_signal=\"metrics\"}[$__rate_interval])) by (processor $grouping)",
              "format": "time_series",
              "interval": "$minstep",
              "intervalFactor": 1,
              "legendFormat": "Incomming: {{processor}} {{service_instance_id}}",
              "range": true,
              "refId": "A"
            },
            {
              "datasource": {
                "type": "prometheus",
                "uid": "$datasource"
              },
              "editorMode": "code",
              "exemplar": false,
              "expr": "0-sum(${metric:value}(otelcol_processor_outgoing_items${suffix_total}{processor=~\"$processor\",job=\"$job\",otel_signal=\"metrics\"}[$__rate_interval])) by (processor $grouping)",
              "format": "time_series",
              "hide": false,
              "interval": "$minstep",
              "intervalFactor": 1,
              "legendFormat": "Outgoing: {{processor}} {{service_instance_id}}",
              "range": true,
              "refId": "B"
            }
          ],
          "title": "Metric Points ${metric:text}",
          "type": "timeseries"
        },
        {
          "datasource": {
            "type": "prometheus",
            "uid": "$datasource"
          },
          "description": "",
          "fieldConfig": {
            "defaults": {
              "color": {
                "mode": "palette-classic"
              },
              "custom": {
                "axisBorderShow": false,
                "axisCenteredZero": false,
                "axisColorMode": "text",
                "axisLabel": "",
                "axisPlacement": "auto",
                "barAlignment": 0,
                "barWidthFactor": 0.6,
                "drawStyle": "line",
                "fillOpacity": 0,
                "gradientMode": "none",
                "hideFrom": {
                  "legend": false,
                  "tooltip": false,
                  "viz": false
                },
                "insertNulls": false,
                "lineInterpolation": "linear",
                "lineWidth": 1,
                "pointSize": 5,
                "scaleDistribution": {
                  "type": "linear"
                },
                "showPoints": "never",
                "spanNulls": true,
                "stacking": {
                  "group": "A",
                  "mode": "none"
                },
                "thresholdsStyle": {
                  "mode": "off"
                }
              },
              "links": [],
              "mappings": [],
              "thresholds": {
                "mode": "absolute",
                "steps": [
                  {
                    "color": "green",
                    "value": null
                  },
                  {
                    "color": "red",
                    "value": 80
                  }
                ]
              },
              "unit": "short"
            },
            "overrides": [
              {
                "matcher": {
                  "id": "byRegexp",
                  "options": "/Refused.*/"
                },
                "properties": [
                  {
                    "id": "color",
                    "value": {
                      "fixedColor": "red",
                      "mode": "fixed"
                    }
                  },
                  {
                    "id": "custom.axisPlacement",
                    "value": "right"
                  }
                ]
              },
              {
                "matcher": {
                  "id": "byRegexp",
                  "options": "/Dropped.*/"
                },
                "properties": [
                  {
                    "id": "color",
                    "value": {
                      "fixedColor": "purple",
                      "mode": "fixed"
                    }
                  },
                  {
                    "id": "custom.axisPlacement",
                    "value": "right"
                  }
                ]
              }
            ]
          },
          "gridPos": {
            "h": 8,
            "w": 8,
            "x": 16,
            "y": 12
          },
          "id": 84,
          "interval": "$minstep",
          "options": {
            "legend": {
              "calcs": [
                "min",
                "max",
                "mean"
              ],
              "displayMode": "table",
              "placement": "bottom",
              "showLegend": true
            },
            "tooltip": {
              "hideZeros": false,
              "mode": "multi",
              "sort": "desc"
            }
          },
          "pluginVersion": "11.5.2",
          "targets": [
            {
              "datasource": {
                "type": "prometheus",
                "uid": "$datasource"
              },
              "editorMode": "code",
              "exemplar": false,
              "expr": "sum(${metric:value}(otelcol_processor_incoming_items${suffix_total}{processor=~\"$processor\",job=\"$job\",otel_signal=\"logs\"}[$__rate_interval])) by (processor $grouping)",
              "format": "time_series",
              "interval": "$minstep",
              "intervalFactor": 1,
              "legendFormat": "Incomming: {{processor}} {{service_instance_id}}",
              "range": true,
              "refId": "A"
            },
            {
              "datasource": {
                "type": "prometheus",
                "uid": "$datasource"
              },
              "editorMode": "code",
              "exemplar": false,
              "expr": "0-sum(${metric:value}(otelcol_processor_outgoing_items${suffix_total}{processor=~\"$processor\",job=\"$job\",otel_signal=\"logs\"}[$__rate_interval])) by (processor $grouping)",
              "format": "time_series",
              "hide": false,
              "interval": "$minstep",
              "intervalFactor": 1,
              "legendFormat": "Outgoing: {{processor}} {{service_instance_id}}",
              "range": true,
              "refId": "B"
            }
          ],
          "title": "Logs Records ${metric:text}",
          "type": "timeseries"
        },
        {
          "datasource": {
            "type": "prometheus",
            "uid": "$datasource"
          },
          "description": "Accepted: count/rate of spans successfully pushed into the next component in the pipeline.\nRefused: count/rate of spans that were rejected by the next component in the pipeline.\nDropped: count/rate of spans that were dropped",
          "fieldConfig": {
            "defaults": {
              "color": {
                "mode": "palette-classic"
              },
              "custom": {
                "axisBorderShow": false,
                "axisCenteredZero": false,
                "axisColorMode": "text",
                "axisLabel": "",
                "axisPlacement": "auto",
                "barAlignment": 0,
                "barWidthFactor": 0.6,
                "drawStyle": "line",
                "fillOpacity": 0,
                "gradientMode": "none",
                "hideFrom": {
                  "legend": false,
                  "tooltip": false,
                  "viz": false
                },
                "insertNulls": false,
                "lineInterpolation": "linear",
                "lineWidth": 1,
                "pointSize": 5,
                "scaleDistribution": {
                  "type": "linear"
                },
                "showPoints": "never",
                "spanNulls": true,
                "stacking": {
                  "group": "A",
                  "mode": "none"
                },
                "thresholdsStyle": {
                  "mode": "off"
                }
              },
              "links": [],
              "mappings": [],
              "min": 0,
              "thresholds": {
                "mode": "absolute",
                "steps": [
                  {
                    "color": "green",
                    "value": null
                  },
                  {
                    "color": "red",
                    "value": 80
                  }
                ]
              },
              "unit": "short"
            },
            "overrides": [
              {
                "matcher": {
                  "id": "byRegexp",
                  "options": "/Refused.*/"
                },
                "properties": [
                  {
                    "id": "color",
                    "value": {
                      "fixedColor": "red",
                      "mode": "fixed"
                    }
                  },
                  {
                    "id": "custom.axisPlacement",
                    "value": "right"
                  }
                ]
              },
              {
                "matcher": {
                  "id": "byRegexp",
                  "options": "/Dropped.*/"
                },
                "properties": [
                  {
                    "id": "color",
                    "value": {
                      "fixedColor": "purple",
                      "mode": "fixed"
                    }
                  },
                  {
                    "id": "custom.axisPlacement",
                    "value": "right"
                  }
                ]
              }
            ]
          },
          "gridPos": {
            "h": 8,
            "w": 8,
            "x": 0,
            "y": 20
          },
          "id": 35,
          "interval": "$minstep",
          "options": {
            "legend": {
              "calcs": [
                "min",
                "max",
                "mean"
              ],
              "displayMode": "table",
              "placement": "bottom",
              "showLegend": true
            },
            "tooltip": {
              "hideZeros": false,
              "mode": "multi",
              "sort": "none"
            }
          },
          "pluginVersion": "11.5.2",
          "targets": [
            {
              "datasource": {
                "type": "prometheus",
                "uid": "$datasource"
              },
              "editorMode": "code",
              "exemplar": false,
              "expr": "sum(${metric:value}(otelcol_processor_accepted_spans${suffix_total}{processor=~\"$processor\",job=\"$job\"}[$__rate_interval])) by (processor $grouping)",
              "format": "time_series",
              "interval": "$minstep",
              "intervalFactor": 1,
              "legendFormat": "Accepted: {{processor}} {{service_instance_id}}",
              "range": true,
              "refId": "A"
            },
            {
              "datasource": {
                "type": "prometheus",
                "uid": "$datasource"
              },
              "editorMode": "code",
              "exemplar": false,
              "expr": "sum(${metric:value}(otelcol_processor_refused_spans${suffix_total}{processor=~\"$processor\",job=\"$job\"}[$__rate_interval])) by (processor $grouping)",
              "format": "time_series",
              "hide": false,
              "interval": "$minstep",
              "intervalFactor": 1,
              "legendFormat": "Refused: {{processor}} {{service_instance_id}}",
              "range": true,
              "refId": "B"
            },
            {
              "datasource": {
                "type": "prometheus",
                "uid": "$datasource"
              },
              "editorMode": "code",
              "exemplar": false,
              "expr": "sum(${metric:value}(otelcol_processor_dropped_spans${suffix_total}{processor=~\"$processor\",job=\"$job\"}[$__rate_interval])) by (processor $grouping)",
              "format": "time_series",
              "hide": false,
              "interval": "$minstep",
              "intervalFactor": 1,
              "legendFormat": "Dropped: {{processor}} {{service_instance_id}}",
              "range": true,
              "refId": "C"
            }
          ],
          "title": "Accepted Spans ${metric:text}",
          "type": "timeseries"
        },
        {
          "datasource": {
            "type": "prometheus",
            "uid": "$datasource"
          },
          "description": "Accepted: count/rate of metric points successfully pushed into the next component in the pipeline.\nRefused: count/rate of metric points that were rejected by the next component in the pipeline.\nDropped: count/rate of metric points that were dropped",
          "fieldConfig": {
            "defaults": {
              "color": {
                "mode": "palette-classic"
              },
              "custom": {
                "axisBorderShow": false,
                "axisCenteredZero": false,
                "axisColorMode": "text",
                "axisLabel": "",
                "axisPlacement": "auto",
                "barAlignment": 0,
                "barWidthFactor": 0.6,
                "drawStyle": "line",
                "fillOpacity": 0,
                "gradientMode": "none",
                "hideFrom": {
                  "legend": false,
                  "tooltip": false,
                  "viz": false
                },
                "insertNulls": false,
                "lineInterpolation": "linear",
                "lineWidth": 1,
                "pointSize": 5,
                "scaleDistribution": {
                  "type": "linear"
                },
                "showPoints": "never",
                "spanNulls": true,
                "stacking": {
                  "group": "A",
                  "mode": "none"
                },
                "thresholdsStyle": {
                  "mode": "off"
                }
              },
              "links": [],
              "mappings": [],
              "min": 0,
              "thresholds": {
                "mode": "absolute",
                "steps": [
                  {
                    "color": "green",
                    "value": null
                  },
                  {
                    "color": "red",
                    "value": 80
                  }
                ]
              },
              "unit": "short"
            },
            "overrides": [
              {
                "matcher": {
                  "id": "byRegexp",
                  "options": "/Refused.*/"
                },
                "properties": [
                  {
                    "id": "color",
                    "value": {
                      "fixedColor": "red",
                      "mode": "fixed"
                    }
                  },
                  {
                    "id": "custom.axisPlacement",
                    "value": "right"
                  }
                ]
              },
              {
                "matcher": {
                  "id": "byRegexp",
                  "options": "/Dropped.*/"
                },
                "properties": [
                  {
                    "id": "color",
                    "value": {
                      "fixedColor": "purple",
                      "mode": "fixed"
                    }
                  },
                  {
                    "id": "custom.axisPlacement",
                    "value": "right"
                  }
                ]
              }
            ]
          },
          "gridPos": {
            "h": 8,
            "w": 8,
            "x": 8,
            "y": 20
          },
          "id": 50,
          "interval": "$minstep",
          "options": {
            "legend": {
              "calcs": [
                "min",
                "max",
                "mean"
              ],
              "displayMode": "table",
              "placement": "bottom",
              "showLegend": true
            },
            "tooltip": {
              "hideZeros": false,
              "mode": "multi",
              "sort": "none"
            }
          },
          "pluginVersion": "11.5.2",
          "targets": [
            {
              "datasource": {
                "type": "prometheus",
                "uid": "$datasource"
              },
              "editorMode": "code",
              "exemplar": false,
              "expr": "sum(${metric:value}(otelcol_processor_accepted_metric_points${suffix_total}{processor=~\"$processor\",job=\"$job\"}[$__rate_interval])) by (processor $grouping)",
              "format": "time_series",
              "interval": "$minstep",
              "intervalFactor": 1,
              "legendFormat": "Accepted: {{processor}} {{service_instance_id}}",
              "range": true,
              "refId": "A"
            },
            {
              "datasource": {
                "type": "prometheus",
                "uid": "$datasource"
              },
              "editorMode": "code",
              "exemplar": false,
              "expr": "sum(${metric:value}(otelcol_processor_refused_metric_points${suffix_total}{processor=~\"$processor\",job=\"$job\"}[$__rate_interval])) by (processor $grouping)",
              "format": "time_series",
              "hide": false,
              "interval": "$minstep",
              "intervalFactor": 1,
              "legendFormat": "Refused: {{processor}} {{service_instance_id}}",
              "range": true,
              "refId": "B"
            },
            {
              "datasource": {
                "type": "prometheus",
                "uid": "$datasource"
              },
              "editorMode": "code",
              "exemplar": false,
              "expr": "sum(${metric:value}(otelcol_processor_dropped_metric_points${suffix_total}{processor=~\"$processor\",job=\"$job\"}[$__rate_interval])) by (processor)",
              "format": "time_series",
              "hide": false,
              "interval": "$minstep",
              "intervalFactor": 1,
              "legendFormat": "Dropped: {{processor}} {{service_instance_id}}",
              "range": true,
              "refId": "C"
            }
          ],
          "title": "Accepted Metric Points ${metric:text}",
          "type": "timeseries"
        },
        {
          "datasource": {
            "type": "prometheus",
            "uid": "$datasource"
          },
          "description": "Accepted: count/rate of log records successfully pushed into the next component in the pipeline.\nRefused: count/rate of log records that were rejected by the next component in the pipeline.\nDropped: count/rate of log records that were dropped",
          "fieldConfig": {
            "defaults": {
              "color": {
                "mode": "palette-classic"
              },
              "custom": {
                "axisBorderShow": false,
                "axisCenteredZero": false,
                "axisColorMode": "text",
                "axisLabel": "",
                "axisPlacement": "auto",
                "barAlignment": 0,
                "barWidthFactor": 0.6,
                "drawStyle": "line",
                "fillOpacity": 0,
                "gradientMode": "none",
                "hideFrom": {
                  "legend": false,
                  "tooltip": false,
                  "viz": false
                },
                "insertNulls": false,
                "lineInterpolation": "linear",
                "lineWidth": 1,
                "pointSize": 5,
                "scaleDistribution": {
                  "type": "linear"
                },
                "showPoints": "never",
                "spanNulls": true,
                "stacking": {
                  "group": "A",
                  "mode": "none"
                },
                "thresholdsStyle": {
                  "mode": "off"
                }
              },
              "links": [],
              "mappings": [],
              "min": 0,
              "thresholds": {
                "mode": "absolute",
                "steps": [
                  {
                    "color": "green",
                    "value": null
                  },
                  {
                    "color": "red",
                    "value": 80
                  }
                ]
              },
              "unit": "short"
            },
            "overrides": [
              {
                "matcher": {
                  "id": "byRegexp",
                  "options": "/Refused.*/"
                },
                "properties": [
                  {
                    "id": "color",
                    "value": {
                      "fixedColor": "red",
                      "mode": "fixed"
                    }
                  },
                  {
                    "id": "custom.axisPlacement",
                    "value": "right"
                  }
                ]
              },
              {
                "matcher": {
                  "id": "byRegexp",
                  "options": "/Dropped.*/"
                },
                "properties": [
                  {
                    "id": "color",
                    "value": {
                      "fixedColor": "purple",
                      "mode": "fixed"
                    }
                  },
                  {
                    "id": "custom.axisPlacement",
                    "value": "right"
                  }
                ]
              }
            ]
          },
          "gridPos": {
            "h": 8,
            "w": 8,
            "x": 16,
            "y": 20
          },
          "id": 51,
          "interval": "$minstep",
          "options": {
            "legend": {
              "calcs": [
                "min",
                "max",
                "mean"
              ],
              "displayMode": "table",
              "placement": "bottom",
              "showLegend": true
            },
            "tooltip": {
              "hideZeros": false,
              "mode": "multi",
              "sort": "none"
            }
          },
          "pluginVersion": "11.5.2",
          "targets": [
            {
              "datasource": {
                "type": "prometheus",
                "uid": "$datasource"
              },
              "editorMode": "code",
              "exemplar": false,
              "expr": "sum(${metric:value}(otelcol_processor_accepted_log_records${suffix_total}{processor=~\"$processor\",job=\"$job\"}[$__rate_interval])) by (processor $grouping)",
              "format": "time_series",
              "interval": "$minstep",
              "intervalFactor": 1,
              "legendFormat": "Accepted: {{processor}} {{service_instance_id}}",
              "range": true,
              "refId": "A"
            },
            {
              "datasource": {
                "type": "prometheus",
                "uid": "$datasource"
              },
              "editorMode": "code",
              "exemplar": false,
              "expr": "sum(${metric:value}(otelcol_processor_refused_log_records${suffix_total}{processor=~\"$processor\",job=\"$job\"}[$__rate_interval])) by (processor $grouping)",
              "format": "time_series",
              "hide": false,
              "interval": "$minstep",
              "intervalFactor": 1,
              "legendFormat": "Refused: {{processor}} {{service_instance_id}}",
              "range": true,
              "refId": "B"
            },
            {
              "datasource": {
                "type": "prometheus",
                "uid": "$datasource"
              },
              "editorMode": "code",
              "exemplar": false,
              "expr": "sum(${metric:value}(otelcol_processor_dropped_log_records${suffix_total}{processor=~\"$processor\",job=\"$job\"}[$__rate_interval])) by (processor)",
              "format": "time_series",
              "hide": false,
              "interval": "$minstep",
              "intervalFactor": 1,
              "legendFormat": "Dropped: {{processor}} {{service_instance_id}}",
              "range": true,
              "refId": "C"
            }
          ],
          "title": "Accepted Log Records ${metric:text}",
          "type": "timeseries"
        },
        {
          "datasource": {
            "type": "prometheus",
            "uid": "$datasource"
          },
          "description": "Number of units in the batch",
          "fieldConfig": {
            "defaults": {
              "custom": {
                "hideFrom": {
                  "legend": false,
                  "tooltip": false,
                  "viz": false
                },
                "scaleDistribution": {
                  "type": "linear"
                }
              },
              "links": []
            },
            "overrides": []
          },
          "gridPos": {
            "h": 8,
            "w": 8,
            "x": 0,
            "y": 28
          },
          "id": 49,
          "interval": "$minstep",
          "maxDataPoints": 50,
          "options": {
            "calculate": false,
            "cellGap": 1,
            "color": {
              "exponent": 0.5,
              "fill": "dark-orange",
              "mode": "scheme",
              "reverse": true,
              "scale": "exponential",
              "scheme": "Reds",
              "steps": 57
            },
            "exemplars": {
              "color": "rgba(255,0,255,0.7)"
            },
            "filterValues": {
              "le": 1e-9
            },
            "legend": {
              "show": true
            },
            "rowsFrame": {
              "layout": "auto"
            },
            "tooltip": {
              "mode": "single",
              "showColorScale": false,
              "yHistogram": false
            },
            "yAxis": {
              "axisPlacement": "left",
              "reverse": false
            }
          },
          "pluginVersion": "11.5.2",
          "targets": [
            {
              "datasource": {
                "type": "prometheus",
                "uid": "$datasource"
              },
              "editorMode": "code",
              "exemplar": false,
              "expr": "sum(increase(otelcol_processor_batch_batch_send_size_bucket{processor=~\"$processor\",job=\"$job\"}[$__rate_interval])) by (le)",
              "format": "heatmap",
              "hide": false,
              "instant": false,
              "interval": "$minstep",
              "intervalFactor": 1,
              "legendFormat": "{{le}}",
              "refId": "B"
            }
          ],
          "title": "Batch Send Size Heatmap",
          "type": "heatmap"
        },
        {
          "datasource": {
            "type": "prometheus",
            "uid": "$datasource"
          },
          "description": "",
          "fieldConfig": {
            "defaults": {
              "color": {
                "mode": "palette-classic"
              },
              "custom": {
                "axisBorderShow": false,
                "axisCenteredZero": false,
                "axisColorMode": "text",
                "axisLabel": "",
                "axisPlacement": "auto",
                "barAlignment": 0,
                "barWidthFactor": 0.6,
                "drawStyle": "line",
                "fillOpacity": 0,
                "gradientMode": "none",
                "hideFrom": {
                  "legend": false,
                  "tooltip": false,
                  "viz": false
                },
                "insertNulls": false,
                "lineInterpolation": "linear",
                "lineWidth": 1,
                "pointSize": 5,
                "scaleDistribution": {
                  "type": "linear"
                },
                "showPoints": "never",
                "spanNulls": true,
                "stacking": {
                  "group": "A",
                  "mode": "none"
                },
                "thresholdsStyle": {
                  "mode": "off"
                }
              },
              "links": [],
              "mappings": [],
              "min": 0,
              "thresholds": {
                "mode": "absolute",
                "steps": [
                  {
                    "color": "green",
                    "value": null
                  },
                  {
                    "color": "red",
                    "value": 80
                  }
                ]
              },
              "unit": "short"
            },
            "overrides": [
              {
                "matcher": {
                  "id": "byRegexp",
                  "options": "/.*count.*/"
                },
                "properties": [
                  {
                    "id": "custom.axisPlacement",
                    "value": "right"
                  }
                ]
              }
            ]
          },
          "gridPos": {
            "h": 8,
            "w": 8,
            "x": 8,
            "y": 28
          },
          "id": 36,
          "interval": "$minstep",
          "options": {
            "legend": {
              "calcs": [
                "min",
                "max",
                "mean"
              ],
              "displayMode": "table",
              "placement": "bottom",
              "showLegend": true
            },
            "tooltip": {
              "hideZeros": false,
              "mode": "multi",
              "sort": "none"
            }
          },
          "pluginVersion": "11.5.2",
          "targets": [
            {
              "datasource": {
                "type": "prometheus",
                "uid": "$datasource"
              },
              "editorMode": "code",
              "exemplar": false,
              "expr": "sum(${metric:value}(otelcol_processor_batch_batch_send_size_count{processor=~\"$processor\",job=\"$job\"}[$__rate_interval])) by (processor $grouping)",
              "format": "time_series",
              "hide": false,
              "instant": false,
              "interval": "$minstep",
              "intervalFactor": 1,
              "legendFormat": "Batch send size count: {{processor}} {{service_instance_id}}",
              "refId": "B"
            },
            {
              "datasource": {
                "type": "prometheus",
                "uid": "$datasource"
              },
              "editorMode": "code",
              "exemplar": false,
              "expr": "sum(${metric:value}(otelcol_processor_batch_batch_send_size_sum{processor=~\"$processor\",job=\"$job\"}[$__rate_interval])) by (processor $grouping)",
              "format": "time_series",
              "hide": false,
              "instant": false,
              "interval": "$minstep",
              "intervalFactor": 1,
              "legendFormat": "Batch send size sum: {{processor}} {{service_instance_id}}",
              "refId": "A"
            }
          ],
          "title": "Batch Metrics 1",
          "type": "timeseries"
        },
        {
          "datasource": {
            "type": "prometheus",
            "uid": "$datasource"
          },
          "description": "Number of times the batch was sent due to a size trigger. Number of times the batch was sent due to a timeout trigger.",
          "fieldConfig": {
            "defaults": {
              "color": {
                "mode": "palette-classic"
              },
              "custom": {
                "axisBorderShow": false,
                "axisCenteredZero": false,
                "axisColorMode": "text",
                "axisLabel": "",
                "axisPlacement": "auto",
                "barAlignment": 0,
                "barWidthFactor": 0.6,
                "drawStyle": "line",
                "fillOpacity": 0,
                "gradientMode": "none",
                "hideFrom": {
                  "legend": false,
                  "tooltip": false,
                  "viz": false
                },
                "insertNulls": false,
                "lineInterpolation": "linear",
                "lineWidth": 1,
                "pointSize": 5,
                "scaleDistribution": {
                  "type": "linear"
                },
                "showPoints": "never",
                "spanNulls": true,
                "stacking": {
                  "group": "A",
                  "mode": "none"
                },
                "thresholdsStyle": {
                  "mode": "off"
                }
              },
              "links": [],
              "mappings": [],
              "min": 0,
              "thresholds": {
                "mode": "absolute",
                "steps": [
                  {
                    "color": "green",
                    "value": null
                  },
                  {
                    "color": "red",
                    "value": 80
                  }
                ]
              },
              "unit": "short"
            },
            "overrides": [
              {
                "matcher": {
                  "id": "byRegexp",
                  "options": "/.*timeout.*/"
                },
                "properties": [
                  {
                    "id": "custom.axisPlacement",
                    "value": "right"
                  }
                ]
              }
            ]
          },
          "gridPos": {
            "h": 8,
            "w": 8,
            "x": 16,
            "y": 28
          },
          "id": 56,
          "interval": "$minstep",
          "options": {
            "legend": {
              "calcs": [
                "min",
                "max",
                "mean"
              ],
              "displayMode": "table",
              "placement": "bottom",
              "showLegend": true
            },
            "tooltip": {
              "hideZeros": false,
              "mode": "multi",
              "sort": "none"
            }
          },
          "pluginVersion": "11.5.2",
          "targets": [
            {
              "datasource": {
                "type": "prometheus",
                "uid": "$datasource"
              },
              "editorMode": "code",
              "exemplar": false,
              "expr": "sum(${metric:value}(otelcol_processor_batch_batch_size_trigger_send${suffix_total}{processor=~\"$processor\",job=\"$job\"}[$__rate_interval])) by (processor $grouping)",
              "format": "time_series",
              "hide": false,
              "instant": false,
              "interval": "$minstep",
              "intervalFactor": 1,
              "legendFormat": "Batch sent due to a size trigger: {{processor}}",
              "refId": "B"
            },
            {
              "datasource": {
                "type": "prometheus",
                "uid": "$datasource"
              },
              "editorMode": "code",
              "exemplar": false,
              "expr": "sum(${metric:value}(otelcol_processor_batch_timeout_trigger_send${suffix_total}{processor=~\"$processor\",job=\"$job\"}[$__rate_interval])) by (processor $grouping)",
              "format": "time_series",
              "hide": false,
              "instant": false,
              "interval": "$minstep",
              "intervalFactor": 1,
              "legendFormat": "Batch sent due to a timeout trigger: {{processor}} {{service_instance_id}}",
              "refId": "A"
            }
          ],
          "title": "Batch Metrics 2",
          "type": "timeseries"
        },
        {
          "collapsed": false,
          "gridPos": {
            "h": 1,
            "w": 24,
            "x": 0,
            "y": 36
          },
          "id": 25,
          "panels": [],
          "title": "Exporters",
          "type": "row"
        },
        {
          "datasource": {
            "type": "prometheus",
            "uid": "$datasource"
          },
          "description": "Sent: count/rate of spans successfully sent to destination.\nEnqueue: count/rate of spans failed to be added to the sending queue.\nFailed: count/rate of spans in failed attempts to send to destination.",
          "fieldConfig": {
            "defaults": {
              "color": {
                "mode": "palette-classic"
              },
              "custom": {
                "axisBorderShow": false,
                "axisCenteredZero": false,
                "axisColorMode": "text",
                "axisLabel": "",
                "axisPlacement": "auto",
                "barAlignment": 0,
                "barWidthFactor": 0.6,
                "drawStyle": "line",
                "fillOpacity": 0,
                "gradientMode": "none",
                "hideFrom": {
                  "legend": false,
                  "tooltip": false,
                  "viz": false
                },
                "insertNulls": false,
                "lineInterpolation": "linear",
                "lineWidth": 1,
                "pointSize": 5,
                "scaleDistribution": {
                  "type": "linear"
                },
                "showPoints": "never",
                "spanNulls": true,
                "stacking": {
                  "group": "A",
                  "mode": "none"
                },
                "thresholdsStyle": {
                  "mode": "off"
                }
              },
              "links": [],
              "mappings": [],
              "min": 0,
              "thresholds": {
                "mode": "absolute",
                "steps": [
                  {
                    "color": "green",
                    "value": null
                  },
                  {
                    "color": "red",
                    "value": 80
                  }
                ]
              },
              "unit": "short"
            },
            "overrides": [
              {
                "matcher": {
                  "id": "byRegexp",
                  "options": "/Failed:.*/"
                },
                "properties": [
                  {
                    "id": "color",
                    "value": {
                      "fixedColor": "red",
                      "mode": "fixed"
                    }
                  },
                  {
                    "id": "custom.axisPlacement",
                    "value": "right"
                  }
                ]
              }
            ]
          },
          "gridPos": {
            "h": 9,
            "w": 8,
            "x": 0,
            "y": 37
          },
          "id": 37,
          "interval": "$minstep",
          "options": {
            "legend": {
              "calcs": [
                "min",
                "max",
                "mean"
              ],
              "displayMode": "table",
              "placement": "bottom",
              "showLegend": true
            },
            "tooltip": {
              "hideZeros": false,
              "mode": "multi",
              "sort": "none"
            }
          },
          "pluginVersion": "11.5.2",
          "targets": [
            {
              "datasource": {
                "type": "prometheus",
                "uid": "$datasource"
              },
              "editorMode": "code",
              "exemplar": false,
              "expr": "sum(${metric:value}(otelcol_exporter_sent_spans${suffix_total}{exporter=~\"$exporter\",job=\"$job\"}[$__rate_interval])) by (exporter $grouping)",
              "format": "time_series",
              "interval": "$minstep",
              "intervalFactor": 1,
              "legendFormat": "Sent: {{exporter}} {{service_instance_id}}",
              "range": true,
              "refId": "A"
            },
            {
              "datasource": {
                "type": "prometheus",
                "uid": "$datasource"
              },
              "editorMode": "code",
              "exemplar": false,
              "expr": "sum(${metric:value}(otelcol_exporter_enqueue_failed_spans${suffix_total}{exporter=~\"$exporter\",job=\"$job\"}[$__rate_interval])) by (exporter $grouping)",
              "format": "time_series",
              "hide": false,
              "interval": "$minstep",
              "intervalFactor": 1,
              "legendFormat": "Enqueue: {{exporter}} {{service_instance_id}}",
              "range": true,
              "refId": "B"
            },
            {
              "datasource": {
                "type": "prometheus",
                "uid": "$datasource"
              },
              "editorMode": "code",
              "exemplar": false,
              "expr": "sum(${metric:value}(otelcol_exporter_send_failed_spans${suffix_total}{exporter=~\"$exporter\",job=\"$job\"}[$__rate_interval])) by (exporter $grouping)",
              "format": "time_series",
              "hide": false,
              "interval": "$minstep",
              "intervalFactor": 1,
              "legendFormat": "Failed: {{exporter}} {{service_instance_id}}",
              "range": true,
              "refId": "C"
            }
          ],
          "title": "Spans ${metric:text}",
          "type": "timeseries"
        },
        {
          "datasource": {
            "type": "prometheus",
            "uid": "$datasource"
          },
          "description": "Sent: count/rate of metric points successfully sent to destination.\nEnqueue: count/rate of metric points failed to be added to the sending queue.\nFailed: count/rate of metric points in failed attempts to send to destination.",
          "fieldConfig": {
            "defaults": {
              "color": {
                "mode": "palette-classic"
              },
              "custom": {
                "axisBorderShow": false,
                "axisCenteredZero": false,
                "axisColorMode": "text",
                "axisLabel": "",
                "axisPlacement": "auto",
                "barAlignment": 0,
                "barWidthFactor": 0.6,
                "drawStyle": "line",
                "fillOpacity": 0,
                "gradientMode": "none",
                "hideFrom": {
                  "legend": false,
                  "tooltip": false,
                  "viz": false
                },
                "insertNulls": false,
                "lineInterpolation": "linear",
                "lineWidth": 1,
                "pointSize": 5,
                "scaleDistribution": {
                  "type": "linear"
                },
                "showPoints": "never",
                "spanNulls": true,
                "stacking": {
                  "group": "A",
                  "mode": "none"
                },
                "thresholdsStyle": {
                  "mode": "off"
                }
              },
              "links": [],
              "mappings": [],
              "min": 0,
              "thresholds": {
                "mode": "absolute",
                "steps": [
                  {
                    "color": "green",
                    "value": null
                  },
                  {
                    "color": "red",
                    "value": 80
                  }
                ]
              },
              "unit": "short"
            },
            "overrides": [
              {
                "matcher": {
                  "id": "byRegexp",
                  "options": "/Failed:.*/"
                },
                "properties": [
                  {
                    "id": "color",
                    "value": {
                      "fixedColor": "red",
                      "mode": "fixed"
                    }
                  },
                  {
                    "id": "custom.axisPlacement",
                    "value": "right"
                  }
                ]
              }
            ]
          },
          "gridPos": {
            "h": 9,
            "w": 8,
            "x": 8,
            "y": 37
          },
          "id": 38,
          "interval": "$minstep",
          "options": {
            "legend": {
              "calcs": [
                "min",
                "max",
                "mean"
              ],
              "displayMode": "table",
              "placement": "bottom",
              "showLegend": true
            },
            "tooltip": {
              "hideZeros": false,
              "mode": "multi",
              "sort": "none"
            }
          },
          "pluginVersion": "11.5.2",
          "targets": [
            {
              "datasource": {
                "type": "prometheus",
                "uid": "$datasource"
              },
              "editorMode": "code",
              "exemplar": false,
              "expr": "sum(${metric:value}(otelcol_exporter_sent_metric_points${suffix_total}{exporter=~\"$exporter\",job=\"$job\"}[$__rate_interval])) by (exporter $grouping)",
              "format": "time_series",
              "interval": "$minstep",
              "intervalFactor": 1,
              "legendFormat": "Sent: {{exporter}} {{service_instance_id}}",
              "range": true,
              "refId": "A"
            },
            {
              "datasource": {
                "type": "prometheus",
                "uid": "$datasource"
              },
              "editorMode": "code",
              "exemplar": false,
              "expr": "sum(${metric:value}(otelcol_exporter_enqueue_failed_metric_points${suffix_total}{exporter=~\"$exporter\",job=\"$job\"}[$__rate_interval])) by (exporter $grouping)",
              "format": "time_series",
              "hide": false,
              "interval": "$minstep",
              "intervalFactor": 1,
              "legendFormat": "Enqueue: {{exporter}} {{service_instance_id}}",
              "range": true,
              "refId": "B"
            },
            {
              "datasource": {
                "type": "prometheus",
                "uid": "$datasource"
              },
              "editorMode": "code",
              "exemplar": false,
              "expr": "sum(${metric:value}(otelcol_exporter_send_failed_metric_points${suffix_total}{exporter=~\"$exporter\",job=\"$job\"}[$__rate_interval])) by (exporter $grouping)",
              "format": "time_series",
              "hide": false,
              "interval": "$minstep",
              "intervalFactor": 1,
              "legendFormat": "Failed: {{exporter}} {{service_instance_id}}",
              "range": true,
              "refId": "C"
            }
          ],
          "title": "Metric Points ${metric:text}",
          "type": "timeseries"
        },
        {
          "datasource": {
            "type": "prometheus",
            "uid": "$datasource"
          },
          "description": "Sent: count/rate of log records successfully sent to destination.\nEnqueue: count/rate of log records failed to be added to the sending queue.\nFailed: count/rate of log records in failed attempts to send to destination.",
          "fieldConfig": {
            "defaults": {
              "color": {
                "mode": "palette-classic"
              },
              "custom": {
                "axisBorderShow": false,
                "axisCenteredZero": false,
                "axisColorMode": "text",
                "axisLabel": "",
                "axisPlacement": "auto",
                "barAlignment": 0,
                "barWidthFactor": 0.6,
                "drawStyle": "line",
                "fillOpacity": 0,
                "gradientMode": "none",
                "hideFrom": {
                  "legend": false,
                  "tooltip": false,
                  "viz": false
                },
                "insertNulls": false,
                "lineInterpolation": "linear",
                "lineWidth": 1,
                "pointSize": 5,
                "scaleDistribution": {
                  "type": "linear"
                },
                "showPoints": "never",
                "spanNulls": true,
                "stacking": {
                  "group": "A",
                  "mode": "none"
                },
                "thresholdsStyle": {
                  "mode": "off"
                }
              },
              "links": [],
              "mappings": [],
              "min": 0,
              "thresholds": {
                "mode": "absolute",
                "steps": [
                  {
                    "color": "green",
                    "value": null
                  },
                  {
                    "color": "red",
                    "value": 80
                  }
                ]
              },
              "unit": "short"
            },
            "overrides": [
              {
                "matcher": {
                  "id": "byRegexp",
                  "options": "/Failed:.*/"
                },
                "properties": [
                  {
                    "id": "color",
                    "value": {
                      "fixedColor": "red",
                      "mode": "fixed"
                    }
                  },
                  {
                    "id": "custom.axisPlacement",
                    "value": "right"
                  }
                ]
              }
            ]
          },
          "gridPos": {
            "h": 9,
            "w": 8,
            "x": 16,
            "y": 37
          },
          "id": 48,
          "interval": "$minstep",
          "options": {
            "legend": {
              "calcs": [
                "min",
                "max",
                "mean"
              ],
              "displayMode": "table",
              "placement": "bottom",
              "showLegend": true
            },
            "tooltip": {
              "hideZeros": false,
              "mode": "multi",
              "sort": "none"
            }
          },
          "pluginVersion": "11.5.2",
          "targets": [
            {
              "datasource": {
                "type": "prometheus",
                "uid": "$datasource"
              },
              "editorMode": "code",
              "exemplar": false,
              "expr": "sum(${metric:value}(otelcol_exporter_sent_log_records${suffix_total}{exporter=~\"$exporter\",job=\"$job\"}[$__rate_interval])) by (exporter $grouping)",
              "format": "time_series",
              "interval": "$minstep",
              "intervalFactor": 1,
              "legendFormat": "Sent: {{exporter}} {{service_instance_id}}",
              "range": true,
              "refId": "A"
            },
            {
              "datasource": {
                "type": "prometheus",
                "uid": "$datasource"
              },
              "editorMode": "code",
              "exemplar": false,
              "expr": "sum(${metric:value}(otelcol_exporter_enqueue_failed_log_records${suffix_total}{exporter=~\"$exporter\",job=\"$job\"}[$__rate_interval])) by (exporter $grouping)",
              "format": "time_series",
              "hide": false,
              "interval": "$minstep",
              "intervalFactor": 1,
              "legendFormat": "Enqueue: {{exporter}} {{service_instance_id}}",
              "range": true,
              "refId": "B"
            },
            {
              "datasource": {
                "type": "prometheus",
                "uid": "$datasource"
              },
              "editorMode": "code",
              "exemplar": false,
              "expr": "sum(${metric:value}(otelcol_exporter_send_failed_log_records${suffix_total}{exporter=~\"$exporter\",job=\"$job\"}[$__rate_interval])) by (exporter $grouping)",
              "format": "time_series",
              "hide": false,
              "interval": "$minstep",
              "intervalFactor": 1,
              "legendFormat": "Failed: {{exporter}} {{service_instance_id}}",
              "range": true,
              "refId": "C"
            }
          ],
          "title": "Log Records ${metric:text}",
          "type": "timeseries"
        },
        {
          "datasource": {
            "type": "prometheus",
            "uid": "$datasource"
          },
          "description": "Current size of the retry queue (in batches)",
          "fieldConfig": {
            "defaults": {
              "color": {
                "mode": "palette-classic"
              },
              "custom": {
                "axisBorderShow": false,
                "axisCenteredZero": false,
                "axisColorMode": "text",
                "axisLabel": "",
                "axisPlacement": "auto",
                "barAlignment": 0,
                "barWidthFactor": 0.6,
                "drawStyle": "line",
                "fillOpacity": 0,
                "gradientMode": "none",
                "hideFrom": {
                  "legend": false,
                  "tooltip": false,
                  "viz": false
                },
                "insertNulls": false,
                "lineInterpolation": "linear",
                "lineWidth": 1,
                "pointSize": 5,
                "scaleDistribution": {
                  "type": "linear"
                },
                "showPoints": "never",
                "spanNulls": true,
                "stacking": {
                  "group": "A",
                  "mode": "none"
                },
                "thresholdsStyle": {
                  "mode": "off"
                }
              },
              "links": [],
              "mappings": [],
              "thresholds": {
                "mode": "absolute",
                "steps": [
                  {
                    "color": "green",
                    "value": null
                  },
                  {
                    "color": "red",
                    "value": 80
                  }
                ]
              },
              "unit": "short"
            },
            "overrides": []
          },
          "gridPos": {
            "h": 9,
            "w": 8,
            "x": 0,
            "y": 46
          },
          "id": 10,
          "options": {
            "legend": {
              "calcs": [
                "min",
                "max",
                "mean"
              ],
              "displayMode": "table",
              "placement": "bottom",
              "showLegend": true
            },
            "tooltip": {
              "hideZeros": false,
              "mode": "multi",
              "sort": "none"
            }
          },
          "pluginVersion": "11.5.2",
          "targets": [
            {
              "datasource": {
                "type": "prometheus",
                "uid": "$datasource"
              },
              "editorMode": "code",
              "exemplar": false,
              "expr": "max(otelcol_exporter_queue_size{exporter=~\"$exporter\",job=\"$job\"}) by (exporter $grouping)",
              "format": "time_series",
              "interval": "$minstep",
              "intervalFactor": 1,
              "legendFormat": "Max queue size: {{exporter}} {{service_instance_id}}",
              "range": true,
              "refId": "A"
            }
          ],
          "title": "Exporter Queue Size",
          "type": "timeseries"
        },
        {
          "datasource": {
            "type": "prometheus",
            "uid": "$datasource"
          },
          "description": "Fixed capacity of the retry queue (in batches)",
          "fieldConfig": {
            "defaults": {
              "color": {
                "mode": "palette-classic"
              },
              "custom": {
                "axisBorderShow": false,
                "axisCenteredZero": false,
                "axisColorMode": "text",
                "axisLabel": "",
                "axisPlacement": "auto",
                "barAlignment": 0,
                "barWidthFactor": 0.6,
                "drawStyle": "line",
                "fillOpacity": 0,
                "gradientMode": "none",
                "hideFrom": {
                  "legend": false,
                  "tooltip": false,
                  "viz": false
                },
                "insertNulls": false,
                "lineInterpolation": "linear",
                "lineWidth": 1,
                "pointSize": 5,
                "scaleDistribution": {
                  "type": "linear"
                },
                "showPoints": "never",
                "spanNulls": true,
                "stacking": {
                  "group": "A",
                  "mode": "none"
                },
                "thresholdsStyle": {
                  "mode": "off"
                }
              },
              "links": [],
              "mappings": [],
              "thresholds": {
                "mode": "absolute",
                "steps": [
                  {
                    "color": "green",
                    "value": null
                  },
                  {
                    "color": "red",
                    "value": 80
                  }
                ]
              },
              "unit": "short"
            },
            "overrides": []
          },
          "gridPos": {
            "h": 9,
            "w": 8,
            "x": 8,
            "y": 46
          },
          "id": 55,
          "options": {
            "legend": {
              "calcs": [
                "min",
                "max",
                "mean"
              ],
              "displayMode": "table",
              "placement": "bottom",
              "showLegend": true
            },
            "tooltip": {
              "hideZeros": false,
              "mode": "multi",
              "sort": "none"
            }
          },
          "pluginVersion": "11.5.2",
          "targets": [
            {
              "datasource": {
                "type": "prometheus",
                "uid": "$datasource"
              },
              "editorMode": "code",
              "exemplar": false,
              "expr": "min(otelcol_exporter_queue_capacity{exporter=~\"$exporter\",job=\"$job\"}) by (exporter $grouping)",
              "format": "time_series",
              "interval": "$minstep",
              "intervalFactor": 1,
              "legendFormat": "Queue capacity: {{exporter}} {{service_instance_id}}",
              "range": true,
              "refId": "A"
            }
          ],
          "title": "Exporter Queue Capacity",
          "type": "timeseries"
        },
        {
          "datasource": {
            "type": "prometheus",
            "uid": "$datasource"
          },
          "description": "",
          "fieldConfig": {
            "defaults": {
              "color": {
                "mode": "palette-classic"
              },
              "custom": {
                "axisBorderShow": false,
                "axisCenteredZero": false,
                "axisColorMode": "text",
                "axisLabel": "",
                "axisPlacement": "auto",
                "barAlignment": 0,
                "barWidthFactor": 0.6,
                "drawStyle": "line",
                "fillOpacity": 0,
                "gradientMode": "none",
                "hideFrom": {
                  "legend": false,
                  "tooltip": false,
                  "viz": false
                },
                "insertNulls": false,
                "lineInterpolation": "linear",
                "lineWidth": 1,
                "pointSize": 5,
                "scaleDistribution": {
                  "type": "linear"
                },
                "showPoints": "never",
                "spanNulls": true,
                "stacking": {
                  "group": "A",
                  "mode": "none"
                },
                "thresholdsStyle": {
                  "mode": "off"
                }
              },
              "links": [],
              "mappings": [],
              "max": 1,
              "thresholds": {
                "mode": "absolute",
                "steps": [
                  {
                    "color": "green",
                    "value": null
                  },
                  {
                    "color": "red",
                    "value": 80
                  }
                ]
              },
              "unit": "percentunit"
            },
            "overrides": []
          },
          "gridPos": {
            "h": 9,
            "w": 8,
            "x": 16,
            "y": 46
          },
          "id": 67,
          "options": {
            "legend": {
              "calcs": [
                "min",
                "max",
                "mean"
              ],
              "displayMode": "table",
              "placement": "bottom",
              "showLegend": true
            },
            "tooltip": {
              "hideZeros": false,
              "mode": "multi",
              "sort": "none"
            }
          },
          "pluginVersion": "11.5.2",
          "targets": [
            {
              "datasource": {
                "type": "prometheus",
                "uid": "$datasource"
              },
              "editorMode": "code",
              "exemplar": false,
              "expr": "max(\r\n    otelcol_exporter_queue_size{\r\n        exporter=~\"$exporter\", job=\"$job\"\r\n    }\r\n) by (exporter $grouping)\r\n/\r\nmin(\r\n    otelcol_exporter_queue_capacity{\r\n        exporter=~\"$exporter\", job=\"$job\"\r\n    }\r\n) by (exporter $grouping)",
              "format": "time_series",
              "interval": "$minstep",
              "intervalFactor": 1,
              "legendFormat": "Queue capacity usage: {{exporter}} {{service_instance_id}}",
              "range": true,
              "refId": "A"
            }
          ],
          "title": "Exporter Queue Usage",
          "type": "timeseries"
        },
        {
          "collapsed": false,
          "gridPos": {
            "h": 1,
            "w": 24,
            "x": 0,
            "y": 55
          },
          "id": 21,
          "panels": [],
          "title": "Collector",
          "type": "row"
        },
        {
          "datasource": {
            "type": "prometheus",
            "uid": "$datasource"
          },
          "description": "Total physical memory (resident set size)",
          "fieldConfig": {
            "defaults": {
              "color": {
                "mode": "palette-classic"
              },
              "custom": {
                "axisBorderShow": false,
                "axisCenteredZero": false,
                "axisColorMode": "text",
                "axisLabel": "",
                "axisPlacement": "auto",
                "barAlignment": 0,
                "barWidthFactor": 0.6,
                "drawStyle": "line",
                "fillOpacity": 0,
                "gradientMode": "none",
                "hideFrom": {
                  "legend": false,
                  "tooltip": false,
                  "viz": false
                },
                "insertNulls": false,
                "lineInterpolation": "linear",
                "lineWidth": 1,
                "pointSize": 5,
                "scaleDistribution": {
                  "type": "linear"
                },
                "showPoints": "never",
                "spanNulls": true,
                "stacking": {
                  "group": "A",
                  "mode": "none"
                },
                "thresholdsStyle": {
                  "mode": "off"
                }
              },
              "links": [],
              "mappings": [],
              "min": 0,
              "thresholds": {
                "mode": "absolute",
                "steps": [
                  {
                    "color": "green",
                    "value": null
                  },
                  {
                    "color": "red",
                    "value": 80
                  }
                ]
              },
              "unit": "bytes"
            },
            "overrides": [
              {
                "matcher": {
                  "id": "byName",
                  "options": "Max Memory RSS "
                },
                "properties": [
                  {
                    "id": "color",
                    "value": {
                      "fixedColor": "red",
                      "mode": "fixed"
                    }
                  },
                  {
                    "id": "custom.fillBelowTo",
                    "value": "Avg Memory RSS "
                  },
                  {
                    "id": "custom.lineWidth",
                    "value": 0
                  },
                  {
                    "id": "custom.fillOpacity",
                    "value": 20
                  }
                ]
              },
              {
                "matcher": {
                  "id": "byName",
                  "options": "Min Memory RSS "
                },
                "properties": [
                  {
                    "id": "color",
                    "value": {
                      "fixedColor": "yellow",
                      "mode": "fixed"
                    }
                  },
                  {
                    "id": "custom.lineWidth",
                    "value": 0
                  }
                ]
              },
              {
                "matcher": {
                  "id": "byName",
                  "options": "Avg Memory RSS "
                },
                "properties": [
                  {
                    "id": "color",
                    "value": {
                      "fixedColor": "orange",
                      "mode": "fixed"
                    }
                  },
                  {
                    "id": "custom.fillBelowTo",
                    "value": "Min Memory RSS "
                  },
                  {
                    "id": "custom.fillOpacity",
                    "value": 20
                  }
                ]
              }
            ]
          },
          "gridPos": {
            "h": 9,
            "w": 8,
            "x": 0,
            "y": 56
          },
          "id": 40,
          "interval": "$minstep",
          "options": {
            "legend": {
              "calcs": [
                "min",
                "max",
                "mean"
              ],
              "displayMode": "table",
              "placement": "bottom",
              "showLegend": true
            },
            "tooltip": {
              "hideZeros": false,
              "mode": "multi",
              "sort": "none"
            }
          },
          "pluginVersion": "11.5.2",
          "targets": [
            {
              "datasource": {
                "type": "prometheus",
                "uid": "$datasource"
              },
              "editorMode": "code",
              "exemplar": false,
              "expr": "max(otelcol_process_memory_rss${suffix_bytes}{job=\"$job\"}) by (job $grouping)",
              "format": "time_series",
              "hide": false,
              "interval": "$minstep",
              "intervalFactor": 1,
              "legendFormat": "Max Memory RSS {{service_instance_id}}",
              "range": true,
              "refId": "C"
            },
            {
              "datasource": {
                "type": "prometheus",
                "uid": "$datasource"
              },
              "editorMode": "code",
              "exemplar": false,
              "expr": "avg(otelcol_process_memory_rss${suffix_bytes}{job=\"$job\"}) by (job $grouping)",
              "format": "time_series",
              "interval": "$minstep",
              "intervalFactor": 1,
              "legendFormat": "Avg Memory RSS {{service_instance_id}}",
              "range": true,
              "refId": "A"
            },
            {
              "datasource": {
                "type": "prometheus",
                "uid": "$datasource"
              },
              "editorMode": "code",
              "exemplar": false,
              "expr": "min(otelcol_process_memory_rss${suffix_bytes}{job=\"$job\"}) by (job $grouping)",
              "format": "time_series",
              "hide": false,
              "interval": "$minstep",
              "intervalFactor": 1,
              "legendFormat": "Min Memory RSS {{service_instance_id}}",
              "range": true,
              "refId": "B"
            }
          ],
          "title": "Total RSS Memory",
          "type": "timeseries"
        },
        {
          "datasource": {
            "type": "prometheus",
            "uid": "$datasource"
          },
          "description": "Total bytes of memory obtained from the OS (see 'go doc runtime.MemStats.Sys')",
          "fieldConfig": {
            "defaults": {
              "color": {
                "mode": "palette-classic"
              },
              "custom": {
                "axisBorderShow": false,
                "axisCenteredZero": false,
                "axisColorMode": "text",
                "axisLabel": "",
                "axisPlacement": "auto",
                "barAlignment": 0,
                "barWidthFactor": 0.6,
                "drawStyle": "line",
                "fillOpacity": 0,
                "gradientMode": "none",
                "hideFrom": {
                  "legend": false,
                  "tooltip": false,
                  "viz": false
                },
                "insertNulls": false,
                "lineInterpolation": "linear",
                "lineWidth": 1,
                "pointSize": 5,
                "scaleDistribution": {
                  "type": "linear"
                },
                "showPoints": "never",
                "spanNulls": true,
                "stacking": {
                  "group": "A",
                  "mode": "none"
                },
                "thresholdsStyle": {
                  "mode": "off"
                }
              },
              "links": [],
              "mappings": [],
              "min": 0,
              "thresholds": {
                "mode": "absolute",
                "steps": [
                  {
                    "color": "green",
                    "value": null
                  },
                  {
                    "color": "red",
                    "value": 80
                  }
                ]
              },
              "unit": "bytes"
            },
            "overrides": [
              {
                "matcher": {
                  "id": "byName",
                  "options": "Max Memory RSS "
                },
                "properties": [
                  {
                    "id": "color",
                    "value": {
                      "fixedColor": "red",
                      "mode": "fixed"
                    }
                  },
                  {
                    "id": "custom.fillBelowTo",
                    "value": "Avg Memory RSS "
                  },
                  {
                    "id": "custom.lineWidth",
                    "value": 0
                  },
                  {
                    "id": "custom.fillOpacity",
                    "value": 20
                  }
                ]
              },
              {
                "matcher": {
                  "id": "byName",
                  "options": "Min Memory RSS "
                },
                "properties": [
                  {
                    "id": "color",
                    "value": {
                      "fixedColor": "yellow",
                      "mode": "fixed"
                    }
                  },
                  {
                    "id": "custom.lineWidth",
                    "value": 0
                  }
                ]
              },
              {
                "matcher": {
                  "id": "byName",
                  "options": "Avg Memory RSS "
                },
                "properties": [
                  {
                    "id": "color",
                    "value": {
                      "fixedColor": "orange",
                      "mode": "fixed"
                    }
                  },
                  {
                    "id": "custom.fillBelowTo",
                    "value": "Min Memory RSS "
                  },
                  {
                    "id": "custom.fillOpacity",
                    "value": 20
                  }
                ]
              }
            ]
          },
          "gridPos": {
            "h": 9,
            "w": 8,
            "x": 8,
            "y": 56
          },
          "id": 52,
          "interval": "$minstep",
          "options": {
            "legend": {
              "calcs": [
                "min",
                "max",
                "mean"
              ],
              "displayMode": "table",
              "placement": "bottom",
              "showLegend": true
            },
            "tooltip": {
              "hideZeros": false,
              "mode": "multi",
              "sort": "none"
            }
          },
          "pluginVersion": "11.5.2",
          "targets": [
            {
              "datasource": {
                "type": "prometheus",
                "uid": "$datasource"
              },
              "editorMode": "code",
              "exemplar": false,
              "expr": "max(otelcol_process_runtime_total_sys_memory_bytes{job=\"$job\"}) by (job $grouping)",
              "format": "time_series",
              "hide": false,
              "interval": "$minstep",
              "intervalFactor": 1,
              "legendFormat": "Max Memory RSS {{service_instance_id}}",
              "range": true,
              "refId": "C"
            },
            {
              "datasource": {
                "type": "prometheus",
                "uid": "$datasource"
              },
              "editorMode": "code",
              "exemplar": false,
              "expr": "avg(otelcol_process_runtime_total_sys_memory_bytes{job=\"$job\"}) by (job $grouping)",
              "format": "time_series",
              "interval": "$minstep",
              "intervalFactor": 1,
              "legendFormat": "Avg Memory RSS {{service_instance_id}}",
              "range": true,
              "refId": "A"
            },
            {
              "datasource": {
                "type": "prometheus",
                "uid": "$datasource"
              },
              "editorMode": "code",
              "exemplar": false,
              "expr": "min(otelcol_process_runtime_total_sys_memory_bytes{job=\"$job\"}) by (job $grouping)",
              "format": "time_series",
              "hide": false,
              "interval": "$minstep",
              "intervalFactor": 1,
              "legendFormat": "Min Memory RSS {{service_instance_id}}",
              "range": true,
              "refId": "B"
            }
          ],
          "title": "Total Runtime Sys Memory",
          "type": "timeseries"
        },
        {
          "datasource": {
            "type": "prometheus",
            "uid": "$datasource"
          },
          "description": "Bytes of allocated heap objects (see 'go doc runtime.MemStats.HeapAlloc')",
          "fieldConfig": {
            "defaults": {
              "color": {
                "mode": "palette-classic"
              },
              "custom": {
                "axisBorderShow": false,
                "axisCenteredZero": false,
                "axisColorMode": "text",
                "axisLabel": "",
                "axisPlacement": "auto",
                "barAlignment": 0,
                "barWidthFactor": 0.6,
                "drawStyle": "line",
                "fillOpacity": 0,
                "gradientMode": "none",
                "hideFrom": {
                  "legend": false,
                  "tooltip": false,
                  "viz": false
                },
                "insertNulls": false,
                "lineInterpolation": "linear",
                "lineWidth": 1,
                "pointSize": 5,
                "scaleDistribution": {
                  "type": "linear"
                },
                "showPoints": "never",
                "spanNulls": true,
                "stacking": {
                  "group": "A",
                  "mode": "none"
                },
                "thresholdsStyle": {
                  "mode": "off"
                }
              },
              "links": [],
              "mappings": [],
              "min": 0,
              "thresholds": {
                "mode": "absolute",
                "steps": [
                  {
                    "color": "green",
                    "value": null
                  },
                  {
                    "color": "red",
                    "value": 80
                  }
                ]
              },
              "unit": "bytes"
            },
            "overrides": [
              {
                "matcher": {
                  "id": "byName",
                  "options": "Max Memory RSS "
                },
                "properties": [
                  {
                    "id": "color",
                    "value": {
                      "fixedColor": "red",
                      "mode": "fixed"
                    }
                  },
                  {
                    "id": "custom.fillBelowTo",
                    "value": "Avg Memory RSS "
                  },
                  {
                    "id": "custom.lineWidth",
                    "value": 0
                  },
                  {
                    "id": "custom.fillOpacity",
                    "value": 20
                  }
                ]
              },
              {
                "matcher": {
                  "id": "byName",
                  "options": "Min Memory RSS "
                },
                "properties": [
                  {
                    "id": "color",
                    "value": {
                      "fixedColor": "yellow",
                      "mode": "fixed"
                    }
                  },
                  {
                    "id": "custom.lineWidth",
                    "value": 0
                  }
                ]
              },
              {
                "matcher": {
                  "id": "byName",
                  "options": "Avg Memory RSS "
                },
                "properties": [
                  {
                    "id": "color",
                    "value": {
                      "fixedColor": "orange",
                      "mode": "fixed"
                    }
                  },
                  {
                    "id": "custom.fillBelowTo",
                    "value": "Min Memory RSS "
                  },
                  {
                    "id": "custom.fillOpacity",
                    "value": 20
                  }
                ]
              }
            ]
          },
          "gridPos": {
            "h": 9,
            "w": 8,
            "x": 16,
            "y": 56
          },
          "id": 53,
          "interval": "$minstep",
          "options": {
            "legend": {
              "calcs": [
                "min",
                "max",
                "mean"
              ],
              "displayMode": "table",
              "placement": "bottom",
              "showLegend": true
            },
            "tooltip": {
              "hideZeros": false,
              "mode": "multi",
              "sort": "none"
            }
          },
          "pluginVersion": "11.5.2",
          "targets": [
            {
              "datasource": {
                "type": "prometheus",
                "uid": "$datasource"
              },
              "editorMode": "code",
              "exemplar": false,
              "expr": "max(otelcol_process_runtime_heap_alloc_bytes{job=\"$job\"}) by (job $grouping)",
              "format": "time_series",
              "hide": false,
              "interval": "$minstep",
              "intervalFactor": 1,
              "legendFormat": "Max Memory RSS {{service_instance_id}}",
              "range": true,
              "refId": "C"
            },
            {
              "datasource": {
                "type": "prometheus",
                "uid": "$datasource"
              },
              "editorMode": "code",
              "exemplar": false,
              "expr": "avg(otelcol_process_runtime_heap_alloc_bytes{job=\"$job\"}) by (job $grouping)",
              "format": "time_series",
              "interval": "$minstep",
              "intervalFactor": 1,
              "legendFormat": "Avg Memory RSS {{service_instance_id}}",
              "range": true,
              "refId": "A"
            },
            {
              "datasource": {
                "type": "prometheus",
                "uid": "$datasource"
              },
              "editorMode": "code",
              "exemplar": false,
              "expr": "min(otelcol_process_runtime_heap_alloc_bytes{job=\"$job\"}) by (job $grouping)",
              "format": "time_series",
              "hide": false,
              "interval": "$minstep",
              "intervalFactor": 1,
              "legendFormat": "Min Memory RSS {{service_instance_id}}",
              "range": true,
              "refId": "B"
            }
          ],
          "title": "Total Runtime Heap Memory",
          "type": "timeseries"
        },
        {
          "datasource": {
            "type": "prometheus",
            "uid": "$datasource"
          },
          "description": "Total CPU user and system time in percentage",
          "fieldConfig": {
            "defaults": {
              "color": {
                "mode": "palette-classic"
              },
              "custom": {
                "axisBorderShow": false,
                "axisCenteredZero": false,
                "axisColorMode": "text",
                "axisLabel": "",
                "axisPlacement": "auto",
                "barAlignment": 0,
                "barWidthFactor": 0.6,
                "drawStyle": "line",
                "fillOpacity": 0,
                "gradientMode": "none",
                "hideFrom": {
                  "legend": false,
                  "tooltip": false,
                  "viz": false
                },
                "insertNulls": false,
                "lineInterpolation": "linear",
                "lineWidth": 1,
                "pointSize": 5,
                "scaleDistribution": {
                  "type": "linear"
                },
                "showPoints": "never",
                "spanNulls": true,
                "stacking": {
                  "group": "A",
                  "mode": "none"
                },
                "thresholdsStyle": {
                  "mode": "off"
                }
              },
              "links": [],
              "mappings": [],
              "min": 0,
              "thresholds": {
                "mode": "absolute",
                "steps": [
                  {
                    "color": "green",
                    "value": null
                  },
                  {
                    "color": "red",
                    "value": 80
                  }
                ]
              },
              "unit": "percent"
            },
            "overrides": [
              {
                "matcher": {
                  "id": "byName",
                  "options": "Max CPU usage "
                },
                "properties": [
                  {
                    "id": "color",
                    "value": {
                      "fixedColor": "red",
                      "mode": "fixed"
                    }
                  },
                  {
                    "id": "custom.fillBelowTo",
                    "value": "Avg CPU usage "
                  },
                  {
                    "id": "custom.lineWidth",
                    "value": 0
                  },
                  {
                    "id": "custom.fillOpacity",
                    "value": 20
                  }
                ]
              },
              {
                "matcher": {
                  "id": "byName",
                  "options": "Avg CPU usage "
                },
                "properties": [
                  {
                    "id": "color",
                    "value": {
                      "fixedColor": "orange",
                      "mode": "fixed"
                    }
                  },
                  {
                    "id": "custom.fillBelowTo",
                    "value": "Min CPU usage "
                  }
                ]
              },
              {
                "matcher": {
                  "id": "byName",
                  "options": "Min CPU usage "
                },
                "properties": [
                  {
                    "id": "color",
                    "value": {
                      "fixedColor": "yellow",
                      "mode": "fixed"
                    }
                  },
                  {
                    "id": "custom.lineWidth",
                    "value": 0
                  }
                ]
              }
            ]
          },
          "gridPos": {
            "h": 9,
            "w": 8,
            "x": 0,
            "y": 65
          },
          "id": 39,
          "interval": "$minstep",
          "options": {
            "legend": {
              "calcs": [
                "min",
                "max",
                "mean"
              ],
              "displayMode": "table",
              "placement": "bottom",
              "showLegend": true
            },
            "tooltip": {
              "hideZeros": false,
              "mode": "multi",
              "sort": "none"
            }
          },
          "pluginVersion": "11.5.2",
          "targets": [
            {
              "datasource": {
                "type": "prometheus",
                "uid": "$datasource"
              },
              "editorMode": "code",
              "exemplar": false,
              "expr": "max(rate(otelcol_process_cpu_seconds${suffix_total}{job=\"$job\"}[$__rate_interval])*100) by (job $grouping)",
              "format": "time_series",
              "hide": false,
              "interval": "$minstep",
              "intervalFactor": 1,
              "legendFormat": "Max CPU usage {{service_instance_id}}",
              "range": true,
              "refId": "B"
            },
            {
              "datasource": {
                "type": "prometheus",
                "uid": "$datasource"
              },
              "editorMode": "code",
              "exemplar": false,
              "expr": "avg(rate(otelcol_process_cpu_seconds${suffix_total}{job=\"$job\"}[$__rate_interval])*100) by (job $grouping)",
              "format": "time_series",
              "hide": false,
              "interval": "$minstep",
              "intervalFactor": 1,
              "legendFormat": "Avg CPU usage {{service_instance_id}}",
              "range": true,
              "refId": "A"
            },
            {
              "datasource": {
                "type": "prometheus",
                "uid": "$datasource"
              },
              "editorMode": "code",
              "exemplar": false,
              "expr": "min(rate(otelcol_process_cpu_seconds${suffix_total}{job=\"$job\"}[$__rate_interval])*100) by (job $grouping)",
              "format": "time_series",
              "hide": false,
              "interval": "$minstep",
              "intervalFactor": 1,
              "legendFormat": "Min CPU usage {{service_instance_id}}",
              "range": true,
              "refId": "C"
            }
          ],
          "title": "CPU Usage",
          "type": "timeseries"
        },
        {
          "datasource": {
            "type": "prometheus",
            "uid": "$datasource"
          },
          "description": "Number of service instances, which are reporting metrics",
          "fieldConfig": {
            "defaults": {
              "color": {
                "mode": "palette-classic"
              },
              "custom": {
                "axisBorderShow": false,
                "axisCenteredZero": false,
                "axisColorMode": "text",
                "axisLabel": "",
                "axisPlacement": "auto",
                "barAlignment": 0,
                "barWidthFactor": 0.6,
                "drawStyle": "line",
                "fillOpacity": 0,
                "gradientMode": "none",
                "hideFrom": {
                  "legend": false,
                  "tooltip": false,
                  "viz": false
                },
                "insertNulls": false,
                "lineInterpolation": "linear",
                "lineWidth": 1,
                "pointSize": 5,
                "scaleDistribution": {
                  "type": "linear"
                },
                "showPoints": "never",
                "spanNulls": true,
                "stacking": {
                  "group": "A",
                  "mode": "none"
                },
                "thresholdsStyle": {
                  "mode": "off"
                }
              },
              "decimals": 0,
              "links": [],
              "mappings": [],
              "min": 0,
              "thresholds": {
                "mode": "absolute",
                "steps": [
                  {
                    "color": "green",
                    "value": null
                  },
                  {
                    "color": "red",
                    "value": 80
                  }
                ]
              },
              "unit": "short"
            },
            "overrides": []
          },
          "gridPos": {
            "h": 9,
            "w": 8,
            "x": 8,
            "y": 65
          },
          "id": 41,
          "interval": "$minstep",
          "options": {
            "legend": {
              "calcs": [
                "min",
                "max",
                "mean"
              ],
              "displayMode": "table",
              "placement": "bottom",
              "showLegend": true
            },
            "tooltip": {
              "hideZeros": false,
              "mode": "multi",
              "sort": "none"
            }
          },
          "pluginVersion": "11.5.2",
          "targets": [
            {
              "datasource": {
                "type": "prometheus",
                "uid": "$datasource"
              },
              "editorMode": "code",
              "exemplar": false,
              "expr": "count(count(otelcol_process_cpu_seconds${suffix_total}{service_instance_id=~\".*\",job=\"$job\"}) by (service_instance_id))",
              "format": "time_series",
              "hide": false,
              "interval": "$minstep",
              "intervalFactor": 1,
              "legendFormat": "Service instance count",
              "range": true,
              "refId": "B"
            }
          ],
          "title": "Service Instance Count",
          "type": "timeseries"
        },
        {
          "datasource": {
            "type": "prometheus",
            "uid": "$datasource"
          },
          "description": "",
          "fieldConfig": {
            "defaults": {
              "color": {
                "mode": "palette-classic"
              },
              "custom": {
                "axisBorderShow": false,
                "axisCenteredZero": false,
                "axisColorMode": "text",
                "axisLabel": "",
                "axisPlacement": "auto",
                "barAlignment": 0,
                "barWidthFactor": 0.6,
                "drawStyle": "line",
                "fillOpacity": 0,
                "gradientMode": "none",
                "hideFrom": {
                  "legend": false,
                  "tooltip": false,
                  "viz": false
                },
                "insertNulls": false,
                "lineInterpolation": "linear",
                "lineWidth": 1,
                "pointSize": 5,
                "scaleDistribution": {
                  "type": "linear"
                },
                "showPoints": "never",
                "spanNulls": true,
                "stacking": {
                  "group": "A",
                  "mode": "none"
                },
                "thresholdsStyle": {
                  "mode": "off"
                }
              },
              "links": [],
              "mappings": [],
              "min": 0,
              "thresholds": {
                "mode": "absolute",
                "steps": [
                  {
                    "color": "green",
                    "value": null
                  },
                  {
                    "color": "red",
                    "value": 80
                  }
                ]
              },
              "unit": "s"
            },
            "overrides": []
          },
          "gridPos": {
            "h": 9,
            "w": 8,
            "x": 16,
            "y": 65
          },
          "id": 54,
          "interval": "$minstep",
          "options": {
            "legend": {
              "calcs": [
                "min",
                "max",
                "mean"
              ],
              "displayMode": "table",
              "placement": "bottom",
              "showLegend": true
            },
            "tooltip": {
              "hideZeros": false,
              "mode": "multi",
              "sort": "none"
            }
          },
          "pluginVersion": "11.5.2",
          "targets": [
            {
              "datasource": {
                "type": "prometheus",
                "uid": "$datasource"
              },
              "editorMode": "code",
              "exemplar": false,
              "expr": "max(otelcol_process_uptime${suffix_seconds}${suffix_total}{service_instance_id=~\".*\",job=\"$job\"}) by (service_instance_id)",
              "format": "time_series",
              "hide": false,
              "interval": "$minstep",
              "intervalFactor": 1,
              "legendFormat": "Service instance uptime: {{service_instance_id}}",
              "range": true,
              "refId": "B"
            }
          ],
          "title": "Uptime by Service Instance",
          "type": "timeseries"
        },
        {
          "datasource": {
            "type": "prometheus",
            "uid": "$datasource"
          },
          "description": "",
          "fieldConfig": {
            "defaults": {
              "color": {
                "mode": "thresholds"
              },
              "custom": {
                "align": "auto",
                "cellOptions": {
                  "type": "auto"
                },
                "inspect": false
              },
              "links": [],
              "mappings": [],
              "min": 0,
              "thresholds": {
                "mode": "absolute",
                "steps": [
                  {
                    "color": "green",
                    "value": null
                  },
                  {
                    "color": "red",
                    "value": 80
                  }
                ]
              },
              "unit": "s"
            },
            "overrides": []
          },
          "gridPos": {
            "h": 5,
            "w": 24,
            "x": 0,
            "y": 74
          },
          "id": 57,
          "interval": "$minstep",
          "options": {
            "cellHeight": "sm",
            "footer": {
              "countRows": false,
              "fields": "",
              "reducer": [
                "sum"
              ],
              "show": false
            },
            "showHeader": true
          },
          "pluginVersion": "11.5.2",
          "targets": [
            {
              "datasource": {
                "type": "prometheus",
                "uid": "$datasource"
              },
              "editorMode": "code",
              "exemplar": false,
              "expr": "max(otelcol_process_uptime${suffix_seconds}${suffix_total}{service_instance_id=~\".*\",job=\"$job\",service_version=\".+\"}) by (service_instance_id,service_name,service_version)\r\nor\r\nmax(\r\n  otelcol_process_uptime${suffix_seconds}${suffix_total}{service_instance_id=~\".*\",job=\"$job\"} \r\n  * on(job, instance) \r\n  group_left(service_version) \r\n  (\r\n    target_info \r\n    * on(job, instance) \r\n    group_left \r\n    label_replace(target_info{}, \"service_instance_id\", \"$1\", \"instance\", \"(.*)\")\r\n  )\r\n) by (service_instance_id, service_name, service_version)",
              "format": "table",
              "hide": false,
              "instant": true,
              "interval": "$minstep",
              "intervalFactor": 1,
              "legendFormat": "__auto",
              "range": false,
              "refId": "B"
            }
          ],
          "title": "Service Instance Details",
          "transformations": [
            {
              "id": "organize",
              "options": {
                "excludeByName": {
                  "Time": true,
                  "Value": true
                },
                "indexByName": {},
                "renameByName": {}
              }
            }
          ],
          "type": "table"
        },
        {
          "collapsed": false,
          "gridPos": {
            "h": 1,
            "w": 24,
            "x": 0,
            "y": 79
          },
          "id": 59,
          "panels": [],
          "title": "Signal flows",
          "type": "row"
        },
        {
          "datasource": {
            "type": "prometheus",
            "uid": "$datasource"
          },
          "description": "Receivers -> Processor(s) -> Exporters (Node Graph panel is beta, so this panel may not show data correctly).",
          "fieldConfig": {
            "defaults": {},
            "overrides": []
          },
          "gridPos": {
            "h": 9,
            "w": 8,
            "x": 0,
            "y": 80
          },
          "id": 58,
          "options": {
            "edges": {},
            "nodes": {
              "mainStatUnit": "flops"
            },
            "zoomMode": "cooperative"
          },
          "pluginVersion": "11.5.2",
          "targets": [
            {
              "datasource": {
                "type": "prometheus",
                "uid": "$datasource"
              },
              "editorMode": "code",
              "exemplar": false,
              "expr": "# receivers\nlabel_replace(\n  label_join(\n    label_join(\n      sum(${metric:value}(\n        otelcol_receiver_accepted_spans${suffix_total}{job=\"$job\"}[$__rate_interval])\n      ) by (receiver)\n      , \"id\", \"-rcv-\", \"transport\", \"receiver\"\n    )\n    , \"title\", \"\", \"transport\", \"receiver\"\n  )\n  , \"icon\", \"arrow-to-right\", \"\", \"\"\n)\n\n# dummy processor\nor\nlabel_replace(\n  label_replace(\n    label_replace(\n      (sum(rate(otelcol_process_uptime${suffix_seconds}${suffix_total}{job=\"$job\"}[$__rate_interval])))\n      , \"id\", \"processor\", \"\", \"\"\n    )\n    , \"title\", \"Processor(s)\", \"\", \"\"\n  )\n  , \"icon\", \"arrow-random\", \"\", \"\"\n)\n\n# exporters\nor\nlabel_replace(\n  label_join(\n    label_join(\n      sum(${metric:value}(\n        otelcol_exporter_sent_spans${suffix_total}{job=\"$job\"}[$__rate_interval])\n      ) by (exporter)\n      , \"id\", \"-exp-\", \"transport\", \"exporter\"\n    )\n    , \"title\", \"\", \"transport\", \"exporter\"\n  )\n  , \"icon\", \"arrow-from-right\", \"\", \"\"\n)",
              "format": "table",
              "hide": false,
              "instant": true,
              "legendFormat": "__auto",
              "range": false,
              "refId": "nodes"
            },
            {
              "datasource": {
                "type": "prometheus",
                "uid": "$datasource"
              },
              "editorMode": "code",
              "exemplar": false,
              "expr": "# receivers -> processor\r\nlabel_join(\r\n    label_replace(\r\n        label_join(\r\n            (sum(rate(otelcol_receiver_accepted_spans${suffix_total}{job=\"$job\"}[$__rate_interval])) by (receiver))\r\n            ,\"source\", \"-rcv-\", \"transport\", \"receiver\"\r\n        )\r\n        ,\"target\", \"processor\", \"\", \"\"\r\n    )\r\n    , \"id\", \"-\", \"source\", \"target\"\r\n)\r\n\r\n# processor -> exporters\r\nor\r\nlabel_join(\r\n    label_replace(\r\n        label_join(\r\n            (sum(rate(otelcol_exporter_sent_spans${suffix_total}{job=\"$job\"}[$__rate_interval])) by (exporter))\r\n            , \"target\", \"-exp-\", \"transport\", \"exporter\"\r\n        )\r\n        , \"source\", \"processor\", \"\", \"\"\r\n    )\r\n    , \"id\", \"-\", \"source\", \"target\"\r\n)",
              "format": "table",
              "hide": false,
              "instant": true,
              "legendFormat": "__auto",
              "range": false,
              "refId": "edges"
            }
          ],
          "title": "Spans Flow",
          "transformations": [
            {
              "id": "renameByRegex",
              "options": {
                "regex": "Value",
                "renamePattern": "mainstat"
              }
            },
            {
              "disabled": true,
              "id": "calculateField",
              "options": {
                "alias": "secondarystat",
                "mode": "reduceRow",
                "reduce": {
                  "include": [
                    "mainstat"
                  ],
                  "reducer": "sum"
                }
              }
            }
          ],
          "type": "nodeGraph"
        },
        {
          "datasource": {
            "type": "prometheus",
            "uid": "$datasource"
          },
          "description": "Receivers -> Processor(s) -> Exporters (Node Graph panel is beta, so this panel may not show data correctly).",
          "fieldConfig": {
            "defaults": {},
            "overrides": []
          },
          "gridPos": {
            "h": 9,
            "w": 8,
            "x": 8,
            "y": 80
          },
          "id": 60,
          "options": {
            "edges": {},
            "nodes": {
              "mainStatUnit": "none"
            },
            "zoomMode": "cooperative"
          },
          "pluginVersion": "11.5.2",
          "targets": [
            {
              "datasource": {
                "type": "prometheus",
                "uid": "$datasource"
              },
              "editorMode": "code",
              "exemplar": false,
              "expr": "# receivers\nlabel_replace(\n  label_join(\n    label_join(\n      (sum(\n        ${metric:value}(otelcol_receiver_accepted_metric_points${suffix_total}{job=\"$job\"}[$__rate_interval])\n      ) by (receiver))\n      , \"id\", \"-rcv-\", \"transport\", \"receiver\"\n    )\n    , \"title\", \"\", \"transport\", \"receiver\"\n  )\n  , \"icon\", \"arrow-to-right\", \"\", \"\"\n)\n\n# dummy processor\nor\nlabel_replace(\n  label_replace(\n    label_replace(\n      (sum(rate(otelcol_process_uptime${suffix_seconds}${suffix_total}{job=\"$job\"}[$__rate_interval])))\n      , \"id\", \"processor\", \"\", \"\"\n    )\n    , \"title\", \"Processor(s)\", \"\", \"\"\n  )\n  , \"icon\", \"arrow-random\", \"\", \"\"\n)\n\n# exporters\nor\nlabel_replace(\n  label_join(\n    label_join(\n      (sum(\n        ${metric:value}(otelcol_exporter_sent_metric_points${suffix_total}{job=\"$job\"}[$__rate_interval])\n      ) by (exporter))\n      , \"id\", \"-exp-\", \"transport\", \"exporter\"\n    )\n    , \"title\", \"\", \"transport\", \"exporter\"\n  )\n  , \"icon\", \"arrow-from-right\", \"\", \"\"\n)",
              "format": "table",
              "hide": false,
              "instant": true,
              "legendFormat": "__auto",
              "range": false,
              "refId": "nodes"
            },
            {
              "datasource": {
                "type": "prometheus",
                "uid": "$datasource"
              },
              "editorMode": "code",
              "exemplar": false,
              "expr": "# receivers -> processor\r\nlabel_join(\r\n    label_replace(\r\n        label_join(\r\n            (sum(rate(otelcol_receiver_accepted_metric_points${suffix_total}{job=\"$job\"}[$__rate_interval])) by (receiver))\r\n            , \"source\", \"-rcv-\", \"transport\", \"receiver\"\r\n        )\r\n        , \"target\", \"processor\", \"\", \"\"\r\n    )\r\n    , \"id\", \"-\", \"source\", \"target\"\r\n)\r\n\r\n# processor -> exporters\r\nor \r\nlabel_join(\r\n    label_replace(\r\n        label_join(\r\n            (sum(rate(otelcol_exporter_sent_metric_points${suffix_total}{job=\"$job\"}[$__rate_interval])) by (exporter))\r\n            , \"target\", \"-exp-\", \"transport\", \"exporter\"\r\n        )\r\n        , \"source\", \"processor\", \"\", \"\"\r\n    )\r\n    , \"id\", \"-\", \"source\", \"target\"\r\n)",
              "format": "table",
              "hide": false,
              "instant": true,
              "legendFormat": "__auto",
              "range": false,
              "refId": "edges"
            }
          ],
          "title": "Metric Points Flow",
          "transformations": [
            {
              "id": "renameByRegex",
              "options": {
                "regex": "Value",
                "renamePattern": "mainstat"
              }
            },
            {
              "disabled": true,
              "id": "calculateField",
              "options": {
                "alias": "secondarystat",
                "mode": "reduceRow",
                "reduce": {
                  "include": [
                    "Value #nodes"
                  ],
                  "reducer": "sum"
                }
              }
            }
          ],
          "type": "nodeGraph"
        },
        {
          "datasource": {
            "type": "prometheus",
            "uid": "$datasource"
          },
          "description": "Receivers -> Processor(s) -> Exporters (Node Graph panel is beta, so this panel may not show data correctly).",
          "fieldConfig": {
            "defaults": {},
            "overrides": []
          },
          "gridPos": {
            "h": 9,
            "w": 8,
            "x": 16,
            "y": 80
          },
          "id": 61,
          "options": {
            "edges": {},
            "nodes": {
              "mainStatUnit": "flops"
            },
            "zoomMode": "cooperative"
          },
          "pluginVersion": "11.5.2",
          "targets": [
            {
              "datasource": {
                "type": "prometheus",
                "uid": "$datasource"
              },
              "editorMode": "code",
              "exemplar": false,
              "expr": "# receivers\nlabel_replace(\n  label_join(\n    label_join(\n      sum(${metric:value}(\n        otelcol_receiver_accepted_log_records${suffix_total}{job=\"$job\"}[$__rate_interval])\n      ) by (receiver)\n      , \"id\", \"-rcv-\", \"transport\", \"receiver\"\n    )\n    , \"title\", \"\", \"transport\", \"receiver\"\n  )\n  , \"icon\", \"arrow-to-right\", \"\", \"\"\n)\n\n# dummy processor\nor\nlabel_replace(\n  label_replace(\n    label_replace(\n      (sum(rate(otelcol_process_uptime${suffix_seconds}${suffix_total}{job=\"$job\"}[$__rate_interval])))\n      , \"id\", \"processor\", \"\", \"\"\n    )\n    , \"title\", \"Processor(s)\", \"\", \"\"\n  )\n  , \"icon\", \"arrow-random\", \"\", \"\"\n)\n\n# exporters\nor\nlabel_replace(\n  label_join(\n    label_join(\n      sum(${metric:value}(\n        otelcol_exporter_sent_log_records${suffix_total}{job=\"$job\"}[$__rate_interval])\n      ) by (exporter)\n      , \"id\", \"-exp-\", \"transport\", \"exporter\"\n    )\n    , \"title\", \"\", \"transport\", \"exporter\"\n  )\n  , \"icon\", \"arrow-from-right\", \"\", \"\"\n)",
              "format": "table",
              "hide": false,
              "instant": true,
              "legendFormat": "__auto",
              "range": false,
              "refId": "nodes"
            },
            {
              "datasource": {
                "type": "prometheus",
                "uid": "$datasource"
              },
              "editorMode": "code",
              "exemplar": false,
              "expr": "# receivers -> processor\r\nlabel_join(\r\n    label_replace(\r\n        label_join(\r\n            (sum(rate(otelcol_receiver_accepted_log_records${suffix_total}{job=\"$job\"}[$__rate_interval])) by (receiver))\r\n            , \"source\", \"-rcv-\", \"transport\", \"receiver\"\r\n        )\r\n        , \"target\", \"processor\", \"\", \"\"\r\n    )\r\n    , \"id\", \"-edg-\", \"source\", \"target\"\r\n)\r\n\r\n# processor -> exporters\r\nor \r\nlabel_join(\r\n    label_replace(\r\n        label_join(\r\n            (sum(rate(otelcol_exporter_sent_log_records${suffix_total}{job=\"$job\"}[$__rate_interval])) by (exporter))\r\n            ,\"target\",\"-exp-\",\"transport\",\"exporter\"\r\n        )\r\n        ,\"source\",\"processor\",\"\",\"\"\r\n    )\r\n    ,\"id\",\"-edg-\",\"source\",\"target\"\r\n)",
              "format": "table",
              "hide": false,
              "instant": true,
              "legendFormat": "__auto",
              "range": false,
              "refId": "edges"
            }
          ],
          "title": "Log Records Flow",
          "transformations": [
            {
              "id": "renameByRegex",
              "options": {
                "regex": "Value",
                "renamePattern": "mainstat"
              }
            },
            {
              "disabled": true,
              "id": "calculateField",
              "options": {
                "alias": "secondarystat",
                "mode": "reduceRow",
                "reduce": {
                  "include": [
                    "mainstat"
                  ],
                  "reducer": "sum"
                }
              }
            }
          ],
          "type": "nodeGraph"
        },
        {
          "collapsed": true,
          "gridPos": {
            "h": 1,
            "w": 24,
            "x": 0,
            "y": 89
          },
          "id": 79,
          "panels": [
            {
              "datasource": {
                "type": "prometheus",
                "uid": "$datasource"
              },
              "description": "",
              "fieldConfig": {
                "defaults": {
                  "color": {
                    "mode": "palette-classic"
                  },
                  "custom": {
                    "axisBorderShow": false,
                    "axisCenteredZero": false,
                    "axisColorMode": "text",
                    "axisLabel": "",
                    "axisPlacement": "auto",
                    "barAlignment": 0,
                    "barWidthFactor": 0.6,
                    "drawStyle": "line",
                    "fillOpacity": 0,
                    "gradientMode": "none",
                    "hideFrom": {
                      "legend": false,
                      "tooltip": false,
                      "viz": false
                    },
                    "insertNulls": false,
                    "lineInterpolation": "linear",
                    "lineWidth": 1,
                    "pointSize": 5,
                    "scaleDistribution": {
                      "type": "linear"
                    },
                    "showPoints": "never",
                    "spanNulls": true,
                    "stacking": {
                      "group": "A",
                      "mode": "none"
                    },
                    "thresholdsStyle": {
                      "mode": "off"
                    }
                  },
                  "links": [],
                  "mappings": [],
                  "min": 0,
                  "thresholds": {
                    "mode": "absolute",
                    "steps": [
                      {
                        "color": "green"
                      },
                      {
                        "color": "red",
                        "value": 80
                      }
                    ]
                  },
                  "unit": "short"
                },
                "overrides": []
              },
              "gridPos": {
                "h": 8,
                "w": 8,
                "x": 0,
                "y": 88
              },
              "id": 32,
              "interval": "$minstep",
              "options": {
                "legend": {
                  "calcs": [
                    "min",
                    "max",
                    "mean"
                  ],
                  "displayMode": "table",
                  "placement": "bottom",
                  "showLegend": true
                },
                "tooltip": {
                  "mode": "multi",
                  "sort": "none"
                }
              },
              "pluginVersion": "11.3.1",
              "targets": [
                {
                  "datasource": {
                    "type": "prometheus",
                    "uid": "$datasource"
                  },
                  "editorMode": "code",
                  "exemplar": false,
                  "expr": "sum(${metric:value}(otelcol_processor_filter_spans_filtered${suffix_total}{job=\"$job\"}[$__rate_interval])) by (filter $grouping)",
                  "format": "time_series",
                  "interval": "$minstep",
                  "intervalFactor": 1,
                  "legendFormat": "Filtered: {{filter}} {{transport}} {{service_instance_id}}",
                  "range": true,
                  "refId": "A"
                }
              ],
              "title": "Spans ${metric:text}",
              "type": "timeseries"
            },
            {
              "datasource": {
                "type": "prometheus",
                "uid": "$datasource"
              },
              "description": "",
              "fieldConfig": {
                "defaults": {
                  "color": {
                    "mode": "palette-classic"
                  },
                  "custom": {
                    "axisBorderShow": false,
                    "axisCenteredZero": false,
                    "axisColorMode": "text",
                    "axisLabel": "",
                    "axisPlacement": "auto",
                    "barAlignment": 0,
                    "barWidthFactor": 0.6,
                    "drawStyle": "line",
                    "fillOpacity": 0,
                    "gradientMode": "none",
                    "hideFrom": {
                      "legend": false,
                      "tooltip": false,
                      "viz": false
                    },
                    "insertNulls": false,
                    "lineInterpolation": "linear",
                    "lineWidth": 1,
                    "pointSize": 5,
                    "scaleDistribution": {
                      "type": "linear"
                    },
                    "showPoints": "never",
                    "spanNulls": true,
                    "stacking": {
                      "group": "A",
                      "mode": "none"
                    },
                    "thresholdsStyle": {
                      "mode": "off"
                    }
                  },
                  "links": [],
                  "mappings": [],
                  "min": 0,
                  "thresholds": {
                    "mode": "absolute",
                    "steps": [
                      {
                        "color": "green"
                      },
                      {
                        "color": "red",
                        "value": 80
                      }
                    ]
                  },
                  "unit": "short"
                },
                "overrides": []
              },
              "gridPos": {
                "h": 8,
                "w": 8,
                "x": 8,
                "y": 88
              },
              "id": 81,
              "interval": "$minstep",
              "options": {
                "legend": {
                  "calcs": [
                    "min",
                    "max",
                    "mean"
                  ],
                  "displayMode": "table",
                  "placement": "bottom",
                  "showLegend": true
                },
                "tooltip": {
                  "mode": "multi",
                  "sort": "none"
                }
              },
              "pluginVersion": "11.3.1",
              "targets": [
                {
                  "datasource": {
                    "type": "prometheus",
                    "uid": "$datasource"
                  },
                  "editorMode": "code",
                  "exemplar": false,
                  "expr": "sum(${metric:value}(otelcol_processor_filter_datapoints_filtered${suffix_total}{job=\"$job\"}[$__rate_interval])) by (filter $grouping)",
                  "format": "time_series",
                  "interval": "$minstep",
                  "intervalFactor": 1,
                  "legendFormat": "Filtered: {{filter}} {{transport}} {{service_instance_id}}",
                  "range": true,
                  "refId": "A"
                }
              ],
              "title": "Metric Points ${metric:text}",
              "type": "timeseries"
            },
            {
              "datasource": {
                "type": "prometheus",
                "uid": "$datasource"
              },
              "description": "",
              "fieldConfig": {
                "defaults": {
                  "color": {
                    "mode": "palette-classic"
                  },
                  "custom": {
                    "axisBorderShow": false,
                    "axisCenteredZero": false,
                    "axisColorMode": "text",
                    "axisLabel": "",
                    "axisPlacement": "auto",
                    "barAlignment": 0,
                    "barWidthFactor": 0.6,
                    "drawStyle": "line",
                    "fillOpacity": 0,
                    "gradientMode": "none",
                    "hideFrom": {
                      "legend": false,
                      "tooltip": false,
                      "viz": false
                    },
                    "insertNulls": false,
                    "lineInterpolation": "linear",
                    "lineWidth": 1,
                    "pointSize": 5,
                    "scaleDistribution": {
                      "type": "linear"
                    },
                    "showPoints": "never",
                    "spanNulls": true,
                    "stacking": {
                      "group": "A",
                      "mode": "none"
                    },
                    "thresholdsStyle": {
                      "mode": "off"
                    }
                  },
                  "links": [],
                  "mappings": [],
                  "min": 0,
                  "thresholds": {
                    "mode": "absolute",
                    "steps": [
                      {
                        "color": "green"
                      },
                      {
                        "color": "red",
                        "value": 80
                      }
                    ]
                  },
                  "unit": "short"
                },
                "overrides": []
              },
              "gridPos": {
                "h": 8,
                "w": 8,
                "x": 16,
                "y": 88
              },
              "id": 82,
              "interval": "$minstep",
              "options": {
                "legend": {
                  "calcs": [
                    "min",
                    "max",
                    "mean"
                  ],
                  "displayMode": "table",
                  "placement": "bottom",
                  "showLegend": true
                },
                "tooltip": {
                  "mode": "multi",
                  "sort": "none"
                }
              },
              "pluginVersion": "11.3.1",
              "targets": [
                {
                  "datasource": {
                    "type": "prometheus",
                    "uid": "$datasource"
                  },
                  "editorMode": "code",
                  "exemplar": false,
                  "expr": "sum(${metric:value}(otelcol_processor_filter_log_records_filtered${suffix_total}{job=\"$job\"}[$__rate_interval])) by (filter $grouping)",
                  "format": "time_series",
                  "interval": "$minstep",
                  "intervalFactor": 1,
                  "legendFormat": "Filtered: {{filter}} {{transport}} {{service_instance_id}}",
                  "range": true,
                  "refId": "A"
                }
              ],
              "title": "Log Records ${metric:text}",
              "type": "timeseries"
            }
          ],
          "title": "Filter processors",
          "type": "row"
        },
        {
          "collapsed": true,
          "gridPos": {
            "h": 1,
            "w": 24,
            "x": 0,
            "y": 90
          },
          "id": 68,
          "panels": [
            {
              "datasource": {
                "type": "prometheus",
                "uid": "$datasource"
              },
              "description": "Measures the number of messages received per RPC. Should be 1 for all non-streaming RPCs. GRPC status codes: https://grpc.github.io/grpc/core/md_doc_statuscodes.html",
              "fieldConfig": {
                "defaults": {
                  "color": {
                    "mode": "palette-classic"
                  },
                  "custom": {
                    "axisBorderShow": false,
                    "axisCenteredZero": false,
                    "axisColorMode": "text",
                    "axisLabel": "",
                    "axisPlacement": "auto",
                    "barAlignment": 0,
                    "barWidthFactor": 0.6,
                    "drawStyle": "line",
                    "fillOpacity": 0,
                    "gradientMode": "none",
                    "hideFrom": {
                      "legend": false,
                      "tooltip": false,
                      "viz": false
                    },
                    "insertNulls": false,
                    "lineInterpolation": "linear",
                    "lineWidth": 1,
                    "pointSize": 5,
                    "scaleDistribution": {
                      "type": "linear"
                    },
                    "showPoints": "never",
                    "spanNulls": true,
                    "stacking": {
                      "group": "A",
                      "mode": "none"
                    },
                    "thresholdsStyle": {
                      "mode": "off"
                    }
                  },
                  "mappings": [],
                  "thresholds": {
                    "mode": "absolute",
                    "steps": [
                      {
                        "color": "green"
                      },
                      {
                        "color": "red",
                        "value": 80
                      }
                    ]
                  }
                },
                "overrides": [
                  {
                    "matcher": {
                      "id": "byName",
                      "options": "0"
                    },
                    "properties": [
                      {
                        "id": "custom.axisPlacement",
                        "value": "right"
                      },
                      {
                        "id": "displayName",
                        "value": "0 - OK"
                      }
                    ]
                  },
                  {
                    "matcher": {
                      "id": "byName",
                      "options": "1"
                    },
                    "properties": [
                      {
                        "id": "displayName",
                        "value": "1 - CANCELLED"
                      }
                    ]
                  },
                  {
                    "matcher": {
                      "id": "byName",
                      "options": "2"
                    },
                    "properties": [
                      {
                        "id": "displayName",
                        "value": "2 - UNKNOWN"
                      }
                    ]
                  },
                  {
                    "matcher": {
                      "id": "byName",
                      "options": "3"
                    },
                    "properties": [
                      {
                        "id": "displayName",
                        "value": "3 - INVALID_ARGUMENT"
                      }
                    ]
                  },
                  {
                    "matcher": {
                      "id": "byName",
                      "options": "4"
                    },
                    "properties": [
                      {
                        "id": "displayName",
                        "value": "4 - DEADLINE_EXCEEDED"
                      }
                    ]
                  },
                  {
                    "matcher": {
                      "id": "byName",
                      "options": "5"
                    },
                    "properties": [
                      {
                        "id": "displayName",
                        "value": "5 - NOT_FOUND"
                      }
                    ]
                  },
                  {
                    "matcher": {
                      "id": "byName",
                      "options": "6"
                    },
                    "properties": [
                      {
                        "id": "displayName",
                        "value": "6 - ALREADY_EXISTS"
                      }
                    ]
                  },
                  {
                    "matcher": {
                      "id": "byName",
                      "options": "7"
                    },
                    "properties": [
                      {
                        "id": "displayName",
                        "value": "7 - PERMISSION_DENIED"
                      }
                    ]
                  },
                  {
                    "matcher": {
                      "id": "byName",
                      "options": "8"
                    },
                    "properties": [
                      {
                        "id": "displayName",
                        "value": "8 - RESOURCE_EXHAUSTED"
                      }
                    ]
                  },
                  {
                    "matcher": {
                      "id": "byName",
                      "options": "9"
                    },
                    "properties": [
                      {
                        "id": "displayName",
                        "value": "9 - FAILED_PRECONDITION"
                      }
                    ]
                  },
                  {
                    "matcher": {
                      "id": "byName",
                      "options": "10"
                    },
                    "properties": [
                      {
                        "id": "displayName",
                        "value": "10 - ABORTED"
                      }
                    ]
                  },
                  {
                    "matcher": {
                      "id": "byName",
                      "options": "11"
                    },
                    "properties": [
                      {
                        "id": "displayName",
                        "value": "11 - OUT_OF_RANGE"
                      }
                    ]
                  },
                  {
                    "matcher": {
                      "id": "byName",
                      "options": "12"
                    },
                    "properties": [
                      {
                        "id": "displayName",
                        "value": "12 - UNIMPLEMENTED"
                      }
                    ]
                  },
                  {
                    "matcher": {
                      "id": "byName",
                      "options": "13"
                    },
                    "properties": [
                      {
                        "id": "displayName",
                        "value": "13 - INTERNAL"
                      }
                    ]
                  },
                  {
                    "matcher": {
                      "id": "byName",
                      "options": "14"
                    },
                    "properties": [
                      {
                        "id": "displayName",
                        "value": "14 - UNAVAILABLE"
                      }
                    ]
                  },
                  {
                    "matcher": {
                      "id": "byName",
                      "options": "15"
                    },
                    "properties": [
                      {
                        "id": "displayName",
                        "value": "15 - DATA_LOSS"
                      }
                    ]
                  },
                  {
                    "matcher": {
                      "id": "byName",
                      "options": "16"
                    },
                    "properties": [
                      {
                        "id": "displayName",
                        "value": "16 - UNAUTHENTICATED"
                      }
                    ]
                  }
                ]
              },
              "gridPos": {
                "h": 9,
                "w": 12,
                "x": 0,
                "y": 291
              },
              "id": 69,
              "interval": "$minstep",
              "options": {
                "legend": {
                  "calcs": [
                    "min",
                    "max",
                    "mean"
                  ],
                  "displayMode": "table",
                  "placement": "bottom",
                  "showLegend": true
                },
                "tooltip": {
                  "mode": "multi",
                  "sort": "none"
                }
              },
              "pluginVersion": "11.3.1",
              "targets": [
                {
                  "datasource": {
                    "type": "prometheus",
                    "uid": "$datasource"
                  },
                  "editorMode": "code",
                  "expr": "sum by(rpc_grpc_status_code) (${metric:value}(${prefix:raw}rpc_server_responses_per_rpc_count{job=\"$job\"}[$__rate_interval]))",
                  "instant": false,
                  "legendFormat": "__auto",
                  "range": true,
                  "refId": "A"
                }
              ],
              "title": "RPC server responses by GRPC status code (receivers)",
              "type": "timeseries"
            },
            {
              "datasource": {
                "type": "prometheus",
                "uid": "$datasource"
              },
              "description": "Measures the number of messages received per RPC. Should be 1 for all non-streaming RPCs. GRPC status codes: https://grpc.github.io/grpc/core/md_doc_statuscodes.html",
              "fieldConfig": {
                "defaults": {
                  "color": {
                    "mode": "palette-classic"
                  },
                  "custom": {
                    "axisBorderShow": false,
                    "axisCenteredZero": false,
                    "axisColorMode": "text",
                    "axisLabel": "",
                    "axisPlacement": "auto",
                    "barAlignment": 0,
                    "barWidthFactor": 0.6,
                    "drawStyle": "line",
                    "fillOpacity": 0,
                    "gradientMode": "none",
                    "hideFrom": {
                      "legend": false,
                      "tooltip": false,
                      "viz": false
                    },
                    "insertNulls": false,
                    "lineInterpolation": "linear",
                    "lineWidth": 1,
                    "pointSize": 5,
                    "scaleDistribution": {
                      "type": "linear"
                    },
                    "showPoints": "never",
                    "spanNulls": true,
                    "stacking": {
                      "group": "A",
                      "mode": "none"
                    },
                    "thresholdsStyle": {
                      "mode": "off"
                    }
                  },
                  "mappings": [],
                  "thresholds": {
                    "mode": "absolute",
                    "steps": [
                      {
                        "color": "green"
                      },
                      {
                        "color": "red",
                        "value": 80
                      }
                    ]
                  }
                },
                "overrides": [
                  {
                    "matcher": {
                      "id": "byName",
                      "options": "0"
                    },
                    "properties": [
                      {
                        "id": "custom.axisPlacement",
                        "value": "right"
                      },
                      {
                        "id": "displayName",
                        "value": "0 - OK"
                      }
                    ]
                  },
                  {
                    "matcher": {
                      "id": "byName",
                      "options": "1"
                    },
                    "properties": [
                      {
                        "id": "displayName",
                        "value": "1 - CANCELLED"
                      }
                    ]
                  },
                  {
                    "matcher": {
                      "id": "byName",
                      "options": "2"
                    },
                    "properties": [
                      {
                        "id": "displayName",
                        "value": "2 - UNKNOWN"
                      }
                    ]
                  },
                  {
                    "matcher": {
                      "id": "byName",
                      "options": "3"
                    },
                    "properties": [
                      {
                        "id": "displayName",
                        "value": "3 - INVALID_ARGUMENT"
                      }
                    ]
                  },
                  {
                    "matcher": {
                      "id": "byName",
                      "options": "4"
                    },
                    "properties": [
                      {
                        "id": "displayName",
                        "value": "4 - DEADLINE_EXCEEDED"
                      }
                    ]
                  },
                  {
                    "matcher": {
                      "id": "byName",
                      "options": "5"
                    },
                    "properties": [
                      {
                        "id": "displayName",
                        "value": "5 - NOT_FOUND"
                      }
                    ]
                  },
                  {
                    "matcher": {
                      "id": "byName",
                      "options": "6"
                    },
                    "properties": [
                      {
                        "id": "displayName",
                        "value": "6 - ALREADY_EXISTS"
                      }
                    ]
                  },
                  {
                    "matcher": {
                      "id": "byName",
                      "options": "7"
                    },
                    "properties": [
                      {
                        "id": "displayName",
                        "value": "7 - PERMISSION_DENIED"
                      }
                    ]
                  },
                  {
                    "matcher": {
                      "id": "byName",
                      "options": "8"
                    },
                    "properties": [
                      {
                        "id": "displayName",
                        "value": "8 - RESOURCE_EXHAUSTED"
                      }
                    ]
                  },
                  {
                    "matcher": {
                      "id": "byName",
                      "options": "9"
                    },
                    "properties": [
                      {
                        "id": "displayName",
                        "value": "9 - FAILED_PRECONDITION"
                      }
                    ]
                  },
                  {
                    "matcher": {
                      "id": "byName",
                      "options": "10"
                    },
                    "properties": [
                      {
                        "id": "displayName",
                        "value": "10 - ABORTED"
                      }
                    ]
                  },
                  {
                    "matcher": {
                      "id": "byName",
                      "options": "11"
                    },
                    "properties": [
                      {
                        "id": "displayName",
                        "value": "11 - OUT_OF_RANGE"
                      }
                    ]
                  },
                  {
                    "matcher": {
                      "id": "byName",
                      "options": "12"
                    },
                    "properties": [
                      {
                        "id": "displayName",
                        "value": "12 - UNIMPLEMENTED"
                      }
                    ]
                  },
                  {
                    "matcher": {
                      "id": "byName",
                      "options": "13"
                    },
                    "properties": [
                      {
                        "id": "displayName",
                        "value": "13 - INTERNAL"
                      }
                    ]
                  },
                  {
                    "matcher": {
                      "id": "byName",
                      "options": "14"
                    },
                    "properties": [
                      {
                        "id": "displayName",
                        "value": "14 - UNAVAILABLE"
                      }
                    ]
                  },
                  {
                    "matcher": {
                      "id": "byName",
                      "options": "15"
                    },
                    "properties": [
                      {
                        "id": "displayName",
                        "value": "15 - DATA_LOSS"
                      }
                    ]
                  },
                  {
                    "matcher": {
                      "id": "byName",
                      "options": "16"
                    },
                    "properties": [
                      {
                        "id": "displayName",
                        "value": "16 - UNAUTHENTICATED"
                      }
                    ]
                  }
                ]
              },
              "gridPos": {
                "h": 9,
                "w": 12,
                "x": 12,
                "y": 291
              },
              "id": 70,
              "interval": "$minstep",
              "options": {
                "legend": {
                  "calcs": [
                    "min",
                    "max",
                    "mean"
                  ],
                  "displayMode": "table",
                  "placement": "bottom",
                  "showLegend": true
                },
                "tooltip": {
                  "mode": "multi",
                  "sort": "none"
                }
              },
              "pluginVersion": "11.3.1",
              "targets": [
                {
                  "datasource": {
                    "type": "prometheus",
                    "uid": "$datasource"
                  },
                  "editorMode": "code",
                  "expr": "sum by(rpc_grpc_status_code) (${metric:value}(${prefix:raw}rpc_client_responses_per_rpc_count{job=\"$job\"}[$__rate_interval]))",
                  "instant": false,
                  "legendFormat": "__auto",
                  "range": true,
                  "refId": "A"
                }
              ],
              "title": "RPC client responses by GRPC status code (exporters)",
              "type": "timeseries"
            },
            {
              "datasource": {
                "type": "prometheus",
                "uid": "$datasource"
              },
              "description": "",
              "fieldConfig": {
                "defaults": {
                  "custom": {
                    "hideFrom": {
                      "legend": false,
                      "tooltip": false,
                      "viz": false
                    },
                    "scaleDistribution": {
                      "type": "linear"
                    }
                  },
                  "links": []
                },
                "overrides": []
              },
              "gridPos": {
                "h": 8,
                "w": 12,
                "x": 0,
                "y": 300
              },
              "id": 72,
              "interval": "$minstep",
              "maxDataPoints": 50,
              "options": {
                "calculate": false,
                "cellGap": 1,
                "color": {
                  "exponent": 0.5,
                  "fill": "dark-orange",
                  "mode": "scheme",
                  "reverse": true,
                  "scale": "exponential",
                  "scheme": "Reds",
                  "steps": 25
                },
                "exemplars": {
                  "color": "rgba(255,0,255,0.7)"
                },
                "filterValues": {
                  "le": 1e-9
                },
                "legend": {
                  "show": true
                },
                "rowsFrame": {
                  "layout": "auto"
                },
                "tooltip": {
                  "mode": "single",
                  "showColorScale": false,
                  "yHistogram": false
                },
                "yAxis": {
                  "axisPlacement": "left",
                  "reverse": false,
                  "unit": "ms"
                }
              },
              "pluginVersion": "11.3.1",
              "targets": [
                {
                  "datasource": {
                    "type": "prometheus",
                    "uid": "$datasource"
                  },
                  "editorMode": "code",
                  "exemplar": false,
                  "expr": "sum(increase(${prefix:raw}rpc_server_duration_bucket{job=\"$job\"}[$__rate_interval])) by (le)",
                  "format": "heatmap",
                  "hide": false,
                  "instant": false,
                  "interval": "$minstep",
                  "intervalFactor": 1,
                  "legendFormat": "{{le}}",
                  "refId": "B"
                }
              ],
              "title": "RPC server duration (receivers)",
              "type": "heatmap"
            },
            {
              "datasource": {
                "type": "prometheus",
                "uid": "$datasource"
              },
              "description": "",
              "fieldConfig": {
                "defaults": {
                  "custom": {
                    "hideFrom": {
                      "legend": false,
                      "tooltip": false,
                      "viz": false
                    },
                    "scaleDistribution": {
                      "type": "linear"
                    }
                  },
                  "links": []
                },
                "overrides": []
              },
              "gridPos": {
                "h": 8,
                "w": 12,
                "x": 12,
                "y": 300
              },
              "id": 74,
              "interval": "$minstep",
              "maxDataPoints": 50,
              "options": {
                "calculate": false,
                "cellGap": 1,
                "color": {
                  "exponent": 0.5,
                  "fill": "dark-orange",
                  "mode": "scheme",
                  "reverse": true,
                  "scale": "exponential",
                  "scheme": "Reds",
                  "steps": 25
                },
                "exemplars": {
                  "color": "rgba(255,0,255,0.7)"
                },
                "filterValues": {
                  "le": 1e-9
                },
                "legend": {
                  "show": true
                },
                "rowsFrame": {
                  "layout": "auto"
                },
                "tooltip": {
                  "mode": "single",
                  "showColorScale": false,
                  "yHistogram": false
                },
                "yAxis": {
                  "axisPlacement": "left",
                  "reverse": false,
                  "unit": "ms"
                }
              },
              "pluginVersion": "11.3.1",
              "targets": [
                {
                  "datasource": {
                    "type": "prometheus",
                    "uid": "$datasource"
                  },
                  "editorMode": "code",
                  "exemplar": false,
                  "expr": "sum(increase(${prefix:raw}rpc_client_duration_bucket{job=\"$job\"}[$__rate_interval])) by (le)",
                  "format": "heatmap",
                  "hide": false,
                  "instant": false,
                  "interval": "$minstep",
                  "intervalFactor": 1,
                  "legendFormat": "{{le}}",
                  "refId": "B"
                }
              ],
              "title": "RPC client duration (exporters)",
              "type": "heatmap"
            },
            {
              "datasource": {
                "type": "prometheus",
                "uid": "$datasource"
              },
              "description": "",
              "fieldConfig": {
                "defaults": {
                  "custom": {
                    "hideFrom": {
                      "legend": false,
                      "tooltip": false,
                      "viz": false
                    },
                    "scaleDistribution": {
                      "type": "linear"
                    }
                  },
                  "links": []
                },
                "overrides": []
              },
              "gridPos": {
                "h": 8,
                "w": 12,
                "x": 0,
                "y": 308
              },
              "id": 73,
              "interval": "$minstep",
              "maxDataPoints": 50,
              "options": {
                "calculate": false,
                "cellGap": 1,
                "color": {
                  "exponent": 0.5,
                  "fill": "dark-orange",
                  "mode": "scheme",
                  "reverse": true,
                  "scale": "exponential",
                  "scheme": "Reds",
                  "steps": 25
                },
                "exemplars": {
                  "color": "rgba(255,0,255,0.7)"
                },
                "filterValues": {
                  "le": 0.1
                },
                "legend": {
                  "show": true
                },
                "rowsFrame": {
                  "layout": "auto"
                },
                "tooltip": {
                  "mode": "single",
                  "showColorScale": false,
                  "yHistogram": false
                },
                "yAxis": {
                  "axisPlacement": "left",
                  "reverse": false,
                  "unit": "bytes"
                }
              },
              "pluginVersion": "11.3.1",
              "targets": [
                {
                  "datasource": {
                    "type": "prometheus",
                    "uid": "$datasource"
                  },
                  "editorMode": "code",
                  "exemplar": false,
                  "expr": "sum(increase(${prefix:raw}rpc_server_request_size_bucket{job=\"$job\"}[$__rate_interval])) by (le)",
                  "format": "heatmap",
                  "hide": false,
                  "instant": false,
                  "interval": "$minstep",
                  "intervalFactor": 1,
                  "legendFormat": "{{le}}",
                  "refId": "B"
                }
              ],
              "title": "RPC server request size (receivers)",
              "type": "heatmap"
            },
            {
              "datasource": {
                "type": "prometheus",
                "uid": "$datasource"
              },
              "description": "",
              "fieldConfig": {
                "defaults": {
                  "custom": {
                    "hideFrom": {
                      "legend": false,
                      "tooltip": false,
                      "viz": false
                    },
                    "scaleDistribution": {
                      "type": "linear"
                    }
                  },
                  "links": []
                },
                "overrides": []
              },
              "gridPos": {
                "h": 8,
                "w": 12,
                "x": 12,
                "y": 308
              },
              "id": 75,
              "interval": "$minstep",
              "maxDataPoints": 50,
              "options": {
                "calculate": false,
                "cellGap": 1,
                "color": {
                  "exponent": 0.5,
                  "fill": "dark-orange",
                  "mode": "scheme",
                  "reverse": true,
                  "scale": "exponential",
                  "scheme": "Reds",
                  "steps": 25
                },
                "exemplars": {
                  "color": "rgba(255,0,255,0.7)"
                },
                "filterValues": {
                  "le": 0.1
                },
                "legend": {
                  "show": true
                },
                "rowsFrame": {
                  "layout": "auto"
                },
                "tooltip": {
                  "mode": "single",
                  "showColorScale": false,
                  "yHistogram": false
                },
                "yAxis": {
                  "axisPlacement": "left",
                  "reverse": false,
                  "unit": "bytes"
                }
              },
              "pluginVersion": "11.3.1",
              "targets": [
                {
                  "datasource": {
                    "type": "prometheus",
                    "uid": "$datasource"
                  },
                  "editorMode": "code",
                  "exemplar": false,
                  "expr": "sum(increase(${prefix:raw}rpc_client_request_size_bucket{job=\"$job\"}[$__rate_interval])) by (le)",
                  "format": "heatmap",
                  "hide": false,
                  "instant": false,
                  "interval": "$minstep",
                  "intervalFactor": 1,
                  "legendFormat": "{{le}}",
                  "refId": "B"
                }
              ],
              "title": "RPC client request size (exporters)",
              "type": "heatmap"
            }
          ],
          "title": "RPC server/client",
          "type": "row"
        },
        {
          "collapsed": true,
          "gridPos": {
            "h": 1,
            "w": 24,
            "x": 0,
            "y": 91
          },
          "id": 77,
          "panels": [
            {
              "datasource": {
                "type": "prometheus",
                "uid": "$datasource"
              },
              "description": "",
              "fieldConfig": {
                "defaults": {
                  "custom": {
                    "hideFrom": {
                      "legend": false,
                      "tooltip": false,
                      "viz": false
                    },
                    "scaleDistribution": {
                      "type": "linear"
                    }
                  },
                  "links": []
                },
                "overrides": []
              },
              "gridPos": {
                "h": 8,
                "w": 12,
                "x": 0,
                "y": 350
              },
              "id": 76,
              "interval": "$minstep",
              "maxDataPoints": 50,
              "options": {
                "calculate": false,
                "cellGap": 1,
                "color": {
                  "exponent": 0.5,
                  "fill": "dark-orange",
                  "mode": "scheme",
                  "reverse": true,
                  "scale": "exponential",
                  "scheme": "Reds",
                  "steps": 25
                },
                "exemplars": {
                  "color": "rgba(255,0,255,0.7)"
                },
                "filterValues": {
                  "le": 1e-9
                },
                "legend": {
                  "show": true
                },
                "rowsFrame": {
                  "layout": "auto"
                },
                "tooltip": {
                  "mode": "single",
                  "showColorScale": false,
                  "yHistogram": false
                },
                "yAxis": {
                  "axisPlacement": "left",
                  "reverse": false,
                  "unit": "ms"
                }
              },
              "pluginVersion": "11.3.1",
              "targets": [
                {
                  "datasource": {
                    "type": "prometheus",
                    "uid": "$datasource"
                  },
                  "editorMode": "code",
                  "exemplar": false,
                  "expr": "sum(increase(${prefix:raw}http_server_duration_bucket{job=\"$job\"}[$__rate_interval])) by (le)",
                  "format": "heatmap",
                  "hide": false,
                  "instant": false,
                  "interval": "$minstep",
                  "intervalFactor": 1,
                  "legendFormat": "__auto",
                  "refId": "B"
                }
              ],
              "title": "HTTP server duration (receivers)",
              "type": "heatmap"
            },
            {
              "datasource": {
                "type": "prometheus",
                "uid": "$datasource"
              },
              "description": "",
              "fieldConfig": {
                "defaults": {
                  "custom": {
                    "hideFrom": {
                      "legend": false,
                      "tooltip": false,
                      "viz": false
                    },
                    "scaleDistribution": {
                      "type": "linear"
                    }
                  },
                  "links": []
                },
                "overrides": []
              },
              "gridPos": {
                "h": 8,
                "w": 12,
                "x": 12,
                "y": 350
              },
              "id": 78,
              "interval": "$minstep",
              "maxDataPoints": 50,
              "options": {
                "calculate": false,
                "cellGap": 1,
                "color": {
                  "exponent": 0.5,
                  "fill": "dark-orange",
                  "mode": "scheme",
                  "reverse": true,
                  "scale": "exponential",
                  "scheme": "Reds",
                  "steps": 25
                },
                "exemplars": {
                  "color": "rgba(255,0,255,0.7)"
                },
                "filterValues": {
                  "le": 1e-9
                },
                "legend": {
                  "show": true
                },
                "rowsFrame": {
                  "layout": "auto"
                },
                "tooltip": {
                  "mode": "single",
                  "showColorScale": false,
                  "yHistogram": false
                },
                "yAxis": {
                  "axisPlacement": "left",
                  "reverse": false,
                  "unit": "ms"
                }
              },
              "pluginVersion": "11.3.1",
              "targets": [
                {
                  "datasource": {
                    "type": "prometheus",
                    "uid": "$datasource"
                  },
                  "editorMode": "code",
                  "exemplar": false,
                  "expr": "sum(increase(${prefix:raw}http_client_duration_bucket{job=\"$job\"}[$__rate_interval])) by (le)",
                  "format": "heatmap",
                  "hide": false,
                  "instant": false,
                  "interval": "$minstep",
                  "intervalFactor": 1,
                  "legendFormat": "{{le}}",
                  "refId": "B"
                }
              ],
              "title": "HTTP client duration (exporters)",
              "type": "heatmap"
            }
          ],
          "title": "HTTP server/client",
          "type": "row"
        },
        {
          "collapsed": true,
          "gridPos": {
            "h": 1,
            "w": 24,
            "x": 0,
            "y": 92
          },
          "id": 63,
          "panels": [
            {
              "datasource": {
                "type": "prometheus",
                "uid": "$datasource"
              },
              "description": "Added: Number of namespace add events received.\nUpdated: Number of namespace update events received.",
              "fieldConfig": {
                "defaults": {
                  "color": {
                    "mode": "palette-classic"
                  },
                  "custom": {
                    "axisBorderShow": false,
                    "axisCenteredZero": false,
                    "axisColorMode": "text",
                    "axisLabel": "",
                    "axisPlacement": "auto",
                    "barAlignment": 0,
                    "barWidthFactor": 0.6,
                    "drawStyle": "line",
                    "fillOpacity": 0,
                    "gradientMode": "none",
                    "hideFrom": {
                      "legend": false,
                      "tooltip": false,
                      "viz": false
                    },
                    "insertNulls": false,
                    "lineInterpolation": "linear",
                    "lineWidth": 1,
                    "pointSize": 5,
                    "scaleDistribution": {
                      "type": "linear"
                    },
                    "showPoints": "never",
                    "spanNulls": true,
                    "stacking": {
                      "group": "A",
                      "mode": "none"
                    },
                    "thresholdsStyle": {
                      "mode": "off"
                    }
                  },
                  "links": [],
                  "mappings": [],
                  "min": 0,
                  "thresholds": {
                    "mode": "absolute",
                    "steps": [
                      {
                        "color": "green"
                      },
                      {
                        "color": "red",
                        "value": 80
                      }
                    ]
                  },
                  "unit": "none"
                },
                "overrides": [
                  {
                    "matcher": {
                      "id": "byRegexp",
                      "options": "/.*updated.*/"
                    },
                    "properties": [
                      {
                        "id": "color",
                        "value": {
                          "fixedColor": "blue",
                          "mode": "fixed"
                        }
                      },
                      {
                        "id": "custom.axisPlacement",
                        "value": "right"
                      }
                    ]
                  }
                ]
              },
              "gridPos": {
                "h": 8,
                "w": 12,
                "x": 0,
                "y": 212
              },
              "id": 64,
              "interval": "$minstep",
              "options": {
                "legend": {
                  "calcs": [
                    "min",
                    "max",
                    "mean"
                  ],
                  "displayMode": "table",
                  "placement": "bottom",
                  "showLegend": true
                },
                "tooltip": {
                  "mode": "multi",
                  "sort": "none"
                }
              },
              "pluginVersion": "11.3.1",
              "targets": [
                {
                  "datasource": {
                    "type": "prometheus",
                    "uid": "$datasource"
                  },
                  "editorMode": "code",
                  "exemplar": false,
                  "expr": "avg(otelcol_otelsvc_k8s_namespace_added${suffix_total}{job=\"$job\"}) by (job $grouping)",
                  "format": "time_series",
                  "hide": false,
                  "interval": "$minstep",
                  "intervalFactor": 1,
                  "legendFormat": "Added: {{transport}} {{service_instance_id}}",
                  "range": true,
                  "refId": "A"
                },
                {
                  "datasource": {
                    "type": "prometheus",
                    "uid": "$datasource"
                  },
                  "editorMode": "code",
                  "exemplar": false,
                  "expr": "avg(otelcol_otelsvc_k8s_namespace_updated${suffix_total}{job=\"$job\"}) by (job $grouping)",
                  "format": "time_series",
                  "hide": false,
                  "interval": "$minstep",
                  "intervalFactor": 1,
                  "legendFormat": "Updated: {{transport}} {{service_instance_id}}",
                  "range": true,
                  "refId": "B"
                }
              ],
              "title": "Namespaces",
              "type": "timeseries"
            },
            {
              "datasource": {
                "type": "prometheus",
                "uid": "$datasource"
              },
              "description": "Added: Number of pod add events received.\nUpdated: Number of pod update events received.\nDeleted: Number of pod delete events received.",
              "fieldConfig": {
                "defaults": {
                  "color": {
                    "mode": "palette-classic"
                  },
                  "custom": {
                    "axisBorderShow": false,
                    "axisCenteredZero": false,
                    "axisColorMode": "text",
                    "axisLabel": "",
                    "axisPlacement": "auto",
                    "barAlignment": 0,
                    "barWidthFactor": 0.6,
                    "drawStyle": "line",
                    "fillOpacity": 0,
                    "gradientMode": "none",
                    "hideFrom": {
                      "legend": false,
                      "tooltip": false,
                      "viz": false
                    },
                    "insertNulls": false,
                    "lineInterpolation": "linear",
                    "lineWidth": 1,
                    "pointSize": 5,
                    "scaleDistribution": {
                      "type": "linear"
                    },
                    "showPoints": "never",
                    "spanNulls": true,
                    "stacking": {
                      "group": "A",
                      "mode": "none"
                    },
                    "thresholdsStyle": {
                      "mode": "off"
                    }
                  },
                  "links": [],
                  "mappings": [],
                  "min": 0,
                  "thresholds": {
                    "mode": "absolute",
                    "steps": [
                      {
                        "color": "green"
                      },
                      {
                        "color": "red",
                        "value": 80
                      }
                    ]
                  },
                  "unit": "none"
                },
                "overrides": []
              },
              "gridPos": {
                "h": 8,
                "w": 12,
                "x": 12,
                "y": 212
              },
              "id": 65,
              "interval": "$minstep",
              "options": {
                "legend": {
                  "calcs": [
                    "min",
                    "max",
                    "mean"
                  ],
                  "displayMode": "table",
                  "placement": "bottom",
                  "showLegend": true
                },
                "tooltip": {
                  "mode": "multi",
                  "sort": "none"
                }
              },
              "pluginVersion": "11.3.1",
              "targets": [
                {
                  "datasource": {
                    "type": "prometheus",
                    "uid": "$datasource"
                  },
                  "editorMode": "code",
                  "exemplar": false,
                  "expr": "sum(otelcol_otelsvc_k8s_pod_added${suffix_total}{job=\"$job\"}) by (job $grouping)",
                  "format": "time_series",
                  "hide": false,
                  "interval": "$minstep",
                  "intervalFactor": 1,
                  "legendFormat": "Added: {{transport}} {{service_instance_id}}",
                  "range": true,
                  "refId": "A"
                },
                {
                  "datasource": {
                    "type": "prometheus",
                    "uid": "$datasource"
                  },
                  "editorMode": "code",
                  "exemplar": false,
                  "expr": "sum(otelcol_otelsvc_k8s_pod_updated${suffix_total}{job=\"$job\"}) by (job $grouping)",
                  "format": "time_series",
                  "hide": false,
                  "interval": "$minstep",
                  "intervalFactor": 1,
                  "legendFormat": "Updated: {{transport}} {{service_instance_id}}",
                  "range": true,
                  "refId": "B"
                },
                {
                  "datasource": {
                    "type": "prometheus",
                    "uid": "$datasource"
                  },
                  "editorMode": "code",
                  "exemplar": false,
                  "expr": "sum(otelcol_otelsvc_k8s_pod_deleted${suffix_total}{job=\"$job\"}) by (job $grouping)",
                  "format": "time_series",
                  "hide": false,
                  "interval": "$minstep",
                  "intervalFactor": 1,
                  "legendFormat": "Deleted: {{transport}} {{service_instance_id}}",
                  "range": true,
                  "refId": "C"
                }
              ],
              "title": "Pods",
              "type": "timeseries"
            }
          ],
          "title": "Kubernetes",
          "type": "row"
        },
        {
          "collapsed": false,
          "gridPos": {
            "h": 1,
            "w": 24,
            "x": 0,
            "y": 93
          },
          "id": 66,
          "panels": [],
          "title": "Documentation",
          "type": "row"
        }
      ],
      "preload": false,
      "refresh": "",
      "schemaVersion": 40,
      "tags": [
        "opentelemetry"
      ],
      "templating": {
        "list": [
          {
            "current": {},
            "includeAll": false,
            "label": "Datasource",
            "name": "datasource",
            "options": [],
            "query": "prometheus",
            "refresh": 1,
            "regex": "",
            "type": "datasource"
          },
          {
            "current": {},
            "datasource": {
              "type": "prometheus",
              "uid": "$datasource"
            },
            "definition": "query_result({__name__=~\"otelcol_process_uptime.*\"})",
            "includeAll": false,
            "label": "Job",
            "name": "job",
            "options": [],
            "query": {
              "qryType": 3,
              "query": "query_result({__name__=~\"otelcol_process_uptime.*\"})",
              "refId": "PrometheusVariableQueryEditor-VariableQuery"
            },
            "refresh": 1,
            "regex": "/.*{.*job=\"([a-zA-Z0-9_-]+)\".*}/",
            "sort": 1,
            "type": "query"
          },
          {
            "auto": true,
            "auto_count": 300,
            "auto_min": "10s",
            "current": {
              "text": "$__auto",
              "value": "$__auto"
            },
            "label": "Min step",
            "name": "minstep",
            "options": [
              {
                "selected": false,
                "text": "10s",
                "value": "10s"
              },
              {
                "selected": false,
                "text": "30s",
                "value": "30s"
              },
              {
                "selected": false,
                "text": "1m",
                "value": "1m"
              },
              {
                "selected": false,
                "text": "5m",
                "value": "5m"
              }
            ],
            "query": "10s,30s,1m,5m",
            "refresh": 2,
            "type": "interval"
          },
          {
            "current": {
              "text": "rate",
              "value": "rate"
            },
            "includeAll": false,
            "label": "Base metric",
            "name": "metric",
            "options": [
              {
                "selected": true,
                "text": "Rate",
                "value": "rate"
              },
              {
                "selected": false,
                "text": "Count",
                "value": "increase"
              }
            ],
            "query": "Rate : rate, Count : increase",
            "type": "custom"
          },
          {
            "allValue": ".*",
            "current": {},
            "datasource": {
              "type": "prometheus",
              "uid": "$datasource"
            },
            "definition": "query_result(avg by (receiver) ({__name__=~\"otelcol_receiver_.+\",job=\"$job\"}))",
            "includeAll": true,
            "label": "Receiver",
            "name": "receiver",
            "options": [],
            "query": {
              "qryType": 3,
              "query": "query_result(avg by (receiver) ({__name__=~\"otelcol_receiver_.+\",job=\"$job\"}))",
              "refId": "PrometheusVariableQueryEditor-VariableQuery"
            },
            "refresh": 2,
            "regex": "/.*receiver=\"(.*)\".*/",
            "sort": 1,
            "type": "query"
          },
          {
            "allValue": ".*",
            "current": {},
            "datasource": {
              "type": "prometheus",
              "uid": "$datasource"
            },
            "definition": "query_result(avg by (processor) ({__name__=~\"otelcol_processor_.+\",job=\"$job\"}))",
            "includeAll": true,
            "label": "Processor",
            "name": "processor",
            "options": [],
            "query": {
              "qryType": 3,
              "query": "query_result(avg by (processor) ({__name__=~\"otelcol_processor_.+\",job=\"$job\"}))",
              "refId": "PrometheusVariableQueryEditor-VariableQuery"
            },
            "refresh": 2,
            "regex": "/.*processor=\"(.*)\".*/",
            "sort": 1,
            "type": "query"
          },
          {
            "allValue": ".*",
            "current": {},
            "datasource": {
              "type": "prometheus",
              "uid": "$datasource"
            },
            "definition": "query_result(avg by (exporter) ({__name__=~\"otelcol_exporter_.+\",job=\"$job\"}))",
            "includeAll": true,
            "label": "Exporter",
            "name": "exporter",
            "options": [],
            "query": {
              "qryType": 3,
              "query": "query_result(avg by (exporter) ({__name__=~\"otelcol_exporter_.+\",job=\"$job\"}))",
              "refId": "PrometheusVariableQueryEditor-VariableQuery"
            },
            "refresh": 2,
            "regex": "/.*exporter=\"(.*)\".*/",
            "sort": 1,
            "type": "query"
          },
          {
            "current": {
              "text": "",
              "value": ""
            },
            "description": "Detailed metrics must be configured in the collector configuration. They add grouping by transport protocol (http/grpc) for receivers. ",
            "includeAll": false,
            "label": "Additional groupping",
            "name": "grouping",
            "options": [
              {
                "selected": true,
                "text": "None (basic metrics)",
                "value": ""
              },
              {
                "selected": false,
                "text": "By transport (detailed metrics)",
                "value": ",transport"
              },
              {
                "selected": false,
                "text": "By service instance id",
                "value": ",service_instance_id"
              }
            ],
            "query": "None (basic metrics) :  , By transport (detailed metrics) : \\,transport, By service instance id : \\,service_instance_id",
            "type": "custom"
          },
          {
            "current": {},
            "datasource": {
              "type": "prometheus",
              "uid": "$datasource"
            },
            "definition": "query_result({__name__=~\"otelcol_process_uptime.+\",job=\"$job\"})",
            "description": "Some exporter(s) configuration(s) may add the metric suffix _total. This variable will detect this case.",
            "hide": 2,
            "includeAll": false,
            "label": "Suffix _total",
            "name": "suffix_total",
            "options": [],
            "query": {
              "qryType": 3,
              "query": "query_result({__name__=~\"otelcol_process_uptime.+\",job=\"$job\"})",
              "refId": "PrometheusVariableQueryEditor-VariableQuery"
            },
            "refresh": 1,
            "regex": "/.*(_total)+{.*/",
            "type": "query"
          },
          {
            "current": {},
            "datasource": {
              "type": "prometheus",
              "uid": "$datasource"
            },
            "definition": "query_result({__name__=~\"otelcol_process_uptime.+\",job=\"$job\"})",
            "description": "Some exporter(s) configuration(s) may add the metric suffix _seconds_total. This variable will detect this \"_seconds\" suffix part.",
            "hide": 2,
            "includeAll": false,
            "label": "Suffix _seconds",
            "name": "suffix_seconds",
            "options": [],
            "query": {
              "qryType": 3,
              "query": "query_result({__name__=~\"otelcol_process_uptime.+\",job=\"$job\"})",
              "refId": "PrometheusVariableQueryEditor-VariableQuery"
            },
            "refresh": 1,
            "regex": "/otelcol_process_uptime(.*)_total{.*/",
            "type": "query"
          },
          {
            "current": {},
            "datasource": {
              "type": "prometheus",
              "uid": "$datasource"
            },
            "definition": "query_result({__name__=~\"otelcol_process_memory_rss.+\",job=\"$job\"})",
            "description": "Some exporter(s) configuration(s) may add the metric suffix _bytes. This variable will detect this \"_bytes\" suffix part.",
            "hide": 2,
            "includeAll": false,
            "label": "Suffix _bytes",
            "name": "suffix_bytes",
            "options": [],
            "query": {
              "qryType": 3,
              "query": "query_result({__name__=~\"otelcol_process_memory_rss.+\",job=\"$job\"})",
              "refId": "PrometheusVariableQueryEditor-VariableQuery"
            },
            "refresh": 1,
            "regex": "/otelcol_process_memory_rss(.*){.*/",
            "type": "query"
          },
          {
            "current": {},
            "datasource": {
              "type": "prometheus",
              "uid": "$datasource"
            },
            "definition": "query_result({__name__=~\".*.*rpc_server_duration_bucket|.*rpc_client_duration_bucket|.*http_server_duration_bucket|.*http_client_duration_bucket.*\",job=\"$job\"})",
            "description": "Some metrics (e.g., RPC, HTTP) may no longer have the otelcol_ prefix. This will detect it. See https://github.com/open-telemetry/opentelemetry-collector/pull/9759",
            "hide": 2,
            "includeAll": false,
            "label": "Prefix",
            "name": "prefix",
            "options": [],
            "query": {
              "qryType": 3,
              "query": "query_result({__name__=~\".*.*rpc_server_duration_bucket|.*rpc_client_duration_bucket|.*http_server_duration_bucket|.*http_client_duration_bucket.*\",job=\"$job\"})",
              "refId": "PrometheusVariableQueryEditor-VariableQuery"
            },
            "refresh": 1,
            "regex": "/(.*)(rpc|http)_(server|client)_duration_bucket.*{.*/",
            "type": "query"
          },
          {
            "baseFilters": [],
            "datasource": {
              "type": "prometheus",
              "uid": "$datasource"
            },
            "filters": [],
            "label": "Ad Hoc",
            "name": "adhoc",
            "type": "adhoc"
          }
        ]
      },
      "time": {
        "from": "now-15m",
        "to": "now"
      },
      "timepicker": {},
      "timezone": "",
      "title": "OpenTelemetry Collector",
      "uid": "BKf2sowmj",
      "version": 1,
      "weekStart": ""
    }
  spanmetrics-dashboard.json: |-
    {
      "annotations": {
        "list": [
          {
            "builtIn": 1,
            "datasource": {
              "type": "grafana",
              "uid": "-- Grafana --"
            },
            "enable": true,
            "hide": true,
            "iconColor": "rgba(0, 211, 255, 1)",
            "name": "Annotations & Alerts",
            "target": {
              "limit": 100,
              "matchAny": false,
              "tags": [],
              "type": "dashboard"
            },
            "type": "dashboard"
          }
        ]
      },
      "description": "Spanmetrics way of demo application view.",
      "editable": true,
      "fiscalYearStartMonth": 0,
      "graphTooltip": 0,
      "id": 3,
      "links": [],
      "panels": [
        {
          "fieldConfig": {
            "defaults": {},
            "overrides": []
          },
          "gridPos": {
            "h": 2,
            "w": 24,
            "x": 0,
            "y": 0
          },
          "id": 26,
          "options": {
            "code": {
              "language": "plaintext",
              "showLineNumbers": false,
              "showMiniMap": false
            },
            "content": "This dashboard uses RED metrics generated for all services by the spanmetrics connector configured in the OpenTelemetry Collector.\n<br>\nChart panels may require 5 minutes after the Demo is started before rendering data.",
            "mode": "html"
          },
          "pluginVersion": "11.4.0",
          "title": "",
          "type": "text"
        },
        {
          "collapsed": false,
          "gridPos": {
            "h": 1,
            "w": 24,
            "x": 0,
            "y": 2
          },
          "id": 24,
          "panels": [],
          "title": "Service Level - Throughput and Latencies",
          "type": "row"
        },
        {
          "datasource": {
            "type": "prometheus",
            "uid": "webstore-metrics"
          },
          "fieldConfig": {
            "defaults": {
              "color": {
                "mode": "continuous-BlYlRd"
              },
              "mappings": [],
              "thresholds": {
                "mode": "absolute",
                "steps": [
                  {
                    "color": "blue",
                    "value": null
                  },
                  {
                    "color": "green",
                    "value": 2
                  },
                  {
                    "color": "#EAB839",
                    "value": 64
                  },
                  {
                    "color": "orange",
                    "value": 128
                  },
                  {
                    "color": "red",
                    "value": 256
                  }
                ]
              },
              "unit": "ms"
            },
            "overrides": []
          },
          "gridPos": {
            "h": 20,
            "w": 12,
            "x": 0,
            "y": 3
          },
          "id": 2,
          "interval": "5m",
          "options": {
            "minVizHeight": 75,
            "minVizWidth": 75,
            "orientation": "auto",
            "reduceOptions": {
              "calcs": [
                "lastNotNull"
              ],
              "fields": "",
              "values": false
            },
            "showThresholdLabels": false,
            "showThresholdMarkers": true,
            "sizing": "auto"
          },
          "pluginVersion": "11.4.0",
          "targets": [
            {
              "datasource": {
                "type": "prometheus",
                "uid": "webstore-metrics"
              },
              "editorMode": "code",
              "exemplar": false,
              "expr": "topk(7,histogram_quantile(0.50, sum(rate(traces_span_metrics_duration_milliseconds_bucket{service_name=~\"$service\", span_name=~\"$span_name\"}[$__rate_interval])) by (le,service_name)))",
              "format": "time_series",
              "hide": true,
              "instant": false,
              "interval": "",
              "legendFormat": "{{service_name}}-quantile_0.50",
              "range": true,
              "refId": "A"
            },
            {
              "datasource": {
                "type": "prometheus",
                "uid": "webstore-metrics"
              },
              "editorMode": "code",
              "exemplar": false,
              "expr": "topk(7,histogram_quantile(0.95, sum(rate(traces_span_metrics_duration_milliseconds_bucket{service_name=~\"$service\", span_name=~\"$span_name\"}[$__range])) by (le,service_name)))",
              "hide": false,
              "instant": true,
              "interval": "",
              "legendFormat": "{{service_name}}",
              "range": false,
              "refId": "B"
            },
            {
              "datasource": {
                "type": "prometheus",
                "uid": "webstore-metrics"
              },
              "editorMode": "code",
              "exemplar": false,
              "expr": "histogram_quantile(0.99, sum(rate(traces_span_metrics_duration_milliseconds_bucket{service_name=~\"$service\", span_name=~\"$span_name\"}[$__rate_interval])) by (le,service_name))",
              "hide": true,
              "interval": "",
              "legendFormat": "quantile99",
              "range": true,
              "refId": "C"
            },
            {
              "datasource": {
                "type": "prometheus",
                "uid": "webstore-metrics"
              },
              "editorMode": "code",
              "exemplar": false,
              "expr": "histogram_quantile(0.999, sum(rate(traces_span_metrics_duration_milliseconds_bucket{service_name=~\"$service\", span_name=~\"$span_name\"}[$__rate_interval])) by (le,service_name))",
              "hide": true,
              "interval": "",
              "legendFormat": "quantile999",
              "range": true,
              "refId": "D"
            }
          ],
          "title": "Top 3x3 - Service Latency - quantile95",
          "type": "gauge"
        },
        {
          "datasource": {
            "type": "prometheus",
            "uid": "webstore-metrics"
          },
          "fieldConfig": {
            "defaults": {
              "color": {
                "mode": "continuous-BlYlRd"
              },
              "decimals": 2,
              "mappings": [],
              "thresholds": {
                "mode": "absolute",
                "steps": [
                  {
                    "color": "green",
                    "value": null
                  },
                  {
                    "color": "super-light-blue",
                    "value": 1
                  },
                  {
                    "color": "#EAB839",
                    "value": 2
                  },
                  {
                    "color": "red",
                    "value": 10
                  }
                ]
              },
              "unit": "reqps"
            },
            "overrides": []
          },
          "gridPos": {
            "h": 13,
            "w": 12,
            "x": 12,
            "y": 3
          },
          "id": 4,
          "interval": "5m",
          "options": {
            "displayMode": "lcd",
            "legend": {
              "calcs": [],
              "displayMode": "list",
              "placement": "bottom",
              "showLegend": false
            },
            "maxVizHeight": 300,
            "minVizHeight": 10,
            "minVizWidth": 0,
            "namePlacement": "auto",
            "orientation": "horizontal",
            "reduceOptions": {
              "calcs": [
                "mean"
              ],
              "fields": "",
              "values": false
            },
            "showUnfilled": true,
            "sizing": "auto",
            "text": {},
            "valueMode": "color"
          },
          "pluginVersion": "11.4.0",
          "targets": [
            {
              "datasource": {
                "type": "prometheus",
                "uid": "webstore-metrics"
              },
              "editorMode": "code",
              "exemplar": false,
              "expr": "topk(7,sum by (service_name) (rate(traces_span_metrics_calls_total{service_name=~\"$service\", span_name=~\"$span_name\"}[$__range])))",
              "format": "time_series",
              "instant": true,
              "interval": "",
              "legendFormat": "{{service_name}}",
              "range": false,
              "refId": "A"
            }
          ],
          "title": "Top 7 Services Mean Rate over Range",
          "type": "bargauge"
        },
        {
          "datasource": {
            "type": "prometheus",
            "uid": "webstore-metrics"
          },
          "fieldConfig": {
            "defaults": {
              "color": {
                "mode": "continuous-reds"
              },
              "decimals": 4,
              "mappings": [],
              "thresholds": {
                "mode": "absolute",
                "steps": [
                  {
                    "color": "green",
                    "value": null
                  },
                  {
                    "color": "#EAB839",
                    "value": 1
                  },
                  {
                    "color": "red",
                    "value": 15
                  }
                ]
              },
              "unit": "reqps"
            },
            "overrides": []
          },
          "gridPos": {
            "h": 7,
            "w": 12,
            "x": 12,
            "y": 16
          },
          "id": 15,
          "interval": "5m",
          "options": {
            "displayMode": "lcd",
            "legend": {
              "calcs": [],
              "displayMode": "list",
              "placement": "bottom",
              "showLegend": false
            },
            "maxVizHeight": 300,
            "minVizHeight": 10,
            "minVizWidth": 0,
            "namePlacement": "auto",
            "orientation": "vertical",
            "reduceOptions": {
              "calcs": [
                "mean"
              ],
              "fields": "",
              "values": false
            },
            "showUnfilled": true,
            "sizing": "auto",
            "text": {},
            "valueMode": "color"
          },
          "pluginVersion": "11.4.0",
          "targets": [
            {
              "datasource": {
                "type": "prometheus",
                "uid": "webstore-metrics"
              },
              "editorMode": "code",
              "exemplar": false,
              "expr": "topk(7,sum(rate(traces_span_metrics_calls_total{status_code=\"STATUS_CODE_ERROR\",service_name=~\"$service\", span_name=~\"$span_name\"}[$__range])) by (service_name))",
              "instant": true,
              "interval": "",
              "legendFormat": "{{service_name}}",
              "range": false,
              "refId": "A"
            }
          ],
          "title": "Top 7 Services Mean ERROR Rate over Range",
          "type": "bargauge"
        },
        {
          "collapsed": false,
          "gridPos": {
            "h": 1,
            "w": 24,
            "x": 0,
            "y": 23
          },
          "id": 14,
          "panels": [],
          "title": "span_names Level - Throughput",
          "type": "row"
        },
        {
          "datasource": {
            "type": "prometheus",
            "uid": "webstore-metrics"
          },
          "description": "",
          "fieldConfig": {
            "defaults": {
              "color": {
                "mode": "thresholds"
              },
              "custom": {
                "align": "auto",
                "cellOptions": {
                  "type": "auto"
                },
                "inspect": false
              },
              "decimals": 2,
              "mappings": [],
              "thresholds": {
                "mode": "absolute",
                "steps": [
                  {
                    "color": "green",
                    "value": null
                  },
                  {
                    "color": "red",
                    "value": 80
                  }
                ]
              },
              "unit": "reqps"
            },
            "overrides": [
              {
                "matcher": {
                  "id": "byName",
                  "options": "bRate"
                },
                "properties": [
                  {
                    "id": "custom.cellOptions",
                    "value": {
                      "mode": "lcd",
                      "type": "gauge"
                    }
                  },
                  {
                    "id": "color",
                    "value": {
                      "mode": "continuous-BlYlRd"
                    }
                  }
                ]
              },
              {
                "matcher": {
                  "id": "byName",
                  "options": "eRate"
                },
                "properties": [
                  {
                    "id": "custom.cellOptions",
                    "value": {
                      "mode": "lcd",
                      "type": "gauge"
                    }
                  },
                  {
                    "id": "color",
                    "value": {
                      "mode": "continuous-RdYlGr"
                    }
                  }
                ]
              },
              {
                "matcher": {
                  "id": "byName",
                  "options": "Error Rate"
                },
                "properties": [
                  {
                    "id": "custom.width",
                    "value": 663
                  }
                ]
              },
              {
                "matcher": {
                  "id": "byName",
                  "options": "Rate"
                },
                "properties": [
                  {
                    "id": "custom.width",
                    "value": 667
                  }
                ]
              },
              {
                "matcher": {
                  "id": "byName",
                  "options": "Service"
                },
                "properties": [
                  {
                    "id": "custom.width"
                  }
                ]
              }
            ]
          },
          "gridPos": {
            "h": 11,
            "w": 24,
            "x": 0,
            "y": 24
          },
          "id": 22,
          "interval": "5m",
          "options": {
            "cellHeight": "sm",
            "footer": {
              "countRows": false,
              "fields": "",
              "reducer": [
                "sum"
              ],
              "show": false
            },
            "showHeader": true,
            "sortBy": []
          },
          "pluginVersion": "11.4.0",
          "targets": [
            {
              "datasource": {
                "type": "prometheus",
                "uid": "webstore-metrics"
              },
              "exemplar": false,
              "expr": "topk(7, sum(rate(traces_span_metrics_calls_total{service_name=~\"$service\", span_name=~\"$span_name\"}[$__range])) by (span_name,service_name)) ",
              "format": "table",
              "instant": true,
              "interval": "",
              "legendFormat": "",
              "refId": "Rate"
            },
            {
              "datasource": {
                "type": "prometheus",
                "uid": "webstore-metrics"
              },
              "exemplar": false,
              "expr": "topk(7, sum(rate(traces_span_metrics_calls_total{status_code=\"STATUS_CODE_ERROR\",service_name=~\"$service\", span_name=~\"$span_name\"}[$__range])) by (span_name,service_name))",
              "format": "table",
              "hide": false,
              "instant": true,
              "interval": "",
              "legendFormat": "",
              "refId": "Error Rate"
            }
          ],
          "title": "Top 7 span_names and Errors  (APM Table)",
          "transformations": [
            {
              "id": "seriesToColumns",
              "options": {
                "byField": "span_name"
              }
            },
            {
              "id": "organize",
              "options": {
                "excludeByName": {
                  "Time 1": true,
                  "Time 2": true
                },
                "indexByName": {},
                "renameByName": {
                  "Value #Error Rate": "Error Rate",
                  "Value #Rate": "Rate",
                  "service_name 1": "Rate in Service",
                  "service_name 2": "Error Rate in Service"
                }
              }
            },
            {
              "id": "calculateField",
              "options": {
                "alias": "bRate",
                "mode": "reduceRow",
                "reduce": {
                  "include": [
                    "Rate"
                  ],
                  "reducer": "sum"
                }
              }
            },
            {
              "id": "calculateField",
              "options": {
                "alias": "eRate",
                "mode": "reduceRow",
                "reduce": {
                  "include": [
                    "Error Rate"
                  ],
                  "reducer": "sum"
                }
              }
            },
            {
              "id": "organize",
              "options": {
                "excludeByName": {
                  "Error Rate": true,
                  "Rate": true,
                  "bRate": false
                },
                "indexByName": {
                  "Error Rate": 4,
                  "Error Rate in Service": 6,
                  "Rate": 1,
                  "Rate in Service": 5,
                  "bRate": 2,
                  "eRate": 3,
                  "span_name": 0
                },
                "renameByName": {
                  "Rate in Service": "Service",
                  "bRate": "Rate",
                  "eRate": "Error Rate",
                  "span_name": "span_name Name"
                }
              }
            },
            {
              "id": "sortBy",
              "options": {
                "fields": {},
                "sort": [
                  {
                    "desc": true,
                    "field": "Rate"
                  }
                ]
              }
            }
          ],
          "type": "table"
        },
        {
          "collapsed": false,
          "gridPos": {
            "h": 1,
            "w": 24,
            "x": 0,
            "y": 35
          },
          "id": 20,
          "panels": [],
          "title": "span_name Level - Latencies",
          "type": "row"
        },
        {
          "datasource": {
            "type": "prometheus",
            "uid": "webstore-metrics"
          },
          "fieldConfig": {
            "defaults": {
              "color": {
                "mode": "continuous-BlYlRd"
              },
              "mappings": [],
              "thresholds": {
                "mode": "absolute",
                "steps": [
                  {
                    "color": "blue"
                  },
                  {
                    "color": "green",
                    "value": 2
                  },
                  {
                    "color": "#EAB839",
                    "value": 64
                  },
                  {
                    "color": "orange",
                    "value": 128
                  },
                  {
                    "color": "red",
                    "value": 256
                  }
                ]
              },
              "unit": "ms"
            },
            "overrides": []
          },
          "gridPos": {
            "h": 13,
            "w": 12,
            "x": 0,
            "y": 36
          },
          "id": 25,
          "interval": "5m",
          "options": {
            "minVizHeight": 75,
            "minVizWidth": 75,
            "orientation": "auto",
            "reduceOptions": {
              "calcs": [
                "lastNotNull"
              ],
              "fields": "",
              "values": false
            },
            "showThresholdLabels": false,
            "showThresholdMarkers": true,
            "sizing": "auto"
          },
          "pluginVersion": "11.3.0",
          "targets": [
            {
              "datasource": {
                "type": "prometheus",
                "uid": "webstore-metrics"
              },
              "editorMode": "code",
              "exemplar": false,
              "expr": "topk(7,histogram_quantile(0.50, sum(rate(traces_span_metrics_duration_milliseconds_bucket{service_name=~\"$service\", span_name=~\"$span_name\"}[$__rate_interval])) by (le,service_name)))",
              "format": "time_series",
              "hide": true,
              "instant": false,
              "interval": "",
              "legendFormat": "{{service_name}}-quantile_0.50",
              "range": true,
              "refId": "A"
            },
            {
              "datasource": {
                "type": "prometheus",
                "uid": "webstore-metrics"
              },
              "editorMode": "code",
              "exemplar": false,
              "expr": "topk(7,histogram_quantile(0.95, sum(rate(traces_span_metrics_duration_milliseconds_bucket{service_name=~\"$service\", span_name=~\"$span_name\"}[$__range])) by (le,span_name)))",
              "hide": false,
              "instant": true,
              "interval": "",
              "legendFormat": "{{span_name}}",
              "range": false,
              "refId": "B"
            },
            {
              "datasource": {
                "type": "prometheus",
                "uid": "webstore-metrics"
              },
              "editorMode": "code",
              "exemplar": false,
              "expr": "histogram_quantile(0.99, sum(rate(traces_span_metrics_duration_milliseconds_bucket{service_name=~\"$service\", span_name=~\"$span_name\"}[$__rate_interval])) by (le,service_name))",
              "hide": true,
              "interval": "",
              "legendFormat": "quantile99",
              "range": true,
              "refId": "C"
            },
            {
              "datasource": {
                "type": "prometheus",
                "uid": "webstore-metrics"
              },
              "editorMode": "code",
              "exemplar": false,
              "expr": "histogram_quantile(0.999, sum(rate(traces_span_metrics_duration_milliseconds_bucket{service_name=~\"$service\", span_name=~\"$span_name\"}[$__rate_interval])) by (le,service_name))",
              "hide": true,
              "interval": "",
              "legendFormat": "quantile999",
              "range": true,
              "refId": "D"
            }
          ],
          "title": "Top 3x3 - span_name Latency - quantile95",
          "type": "gauge"
        },
        {
          "datasource": {
            "type": "prometheus",
            "uid": "webstore-metrics"
          },
          "fieldConfig": {
            "defaults": {
              "color": {
                "mode": "continuous-BlYlRd"
              },
              "decimals": 2,
              "mappings": [],
              "thresholds": {
                "mode": "absolute",
                "steps": [
                  {
                    "color": "green"
                  },
                  {
                    "color": "red",
                    "value": 80
                  }
                ]
              },
              "unit": "ms"
            },
            "overrides": []
          },
          "gridPos": {
            "h": 13,
            "w": 12,
            "x": 12,
            "y": 36
          },
          "id": 10,
          "interval": "5m",
          "options": {
            "displayMode": "lcd",
            "legend": {
              "calcs": [],
              "displayMode": "list",
              "placement": "bottom",
              "showLegend": false
            },
            "maxVizHeight": 300,
            "minVizHeight": 10,
            "minVizWidth": 0,
            "namePlacement": "auto",
            "orientation": "horizontal",
            "reduceOptions": {
              "calcs": [
                "mean"
              ],
              "fields": "",
              "values": false
            },
            "showUnfilled": true,
            "sizing": "auto",
            "valueMode": "color"
          },
          "pluginVersion": "11.3.0",
          "targets": [
            {
              "datasource": {
                "type": "prometheus",
                "uid": "webstore-metrics"
              },
              "editorMode": "code",
              "exemplar": false,
              "expr": "topk(7, sum by (span_name,service_name)(increase(traces_span_metrics_duration_milliseconds_sum{service_name=~\"${service}\", span_name=~\"$span_name\"}[5m]) / increase(traces_span_metrics_duration_milliseconds_count{service_name=~\"${service}\",span_name=~\"$span_name\"}[5m\n])))",
              "instant": true,
              "interval": "",
              "legendFormat": "{{span_name}} [{{service_name}}]",
              "range": false,
              "refId": "A"
            }
          ],
          "title": "Top 7 Highest Endpoint Latencies  Mean Over Range ",
          "type": "bargauge"
        },
        {
          "datasource": {
            "type": "prometheus",
            "uid": "webstore-metrics"
          },
          "fieldConfig": {
            "defaults": {
              "color": {
                "mode": "palette-classic"
              },
              "custom": {
                "axisBorderShow": false,
                "axisCenteredZero": false,
                "axisColorMode": "text",
                "axisLabel": "",
                "axisPlacement": "auto",
                "barAlignment": 0,
                "barWidthFactor": 0.6,
                "drawStyle": "line",
                "fillOpacity": 15,
                "gradientMode": "none",
                "hideFrom": {
                  "legend": false,
                  "tooltip": false,
                  "viz": false
                },
                "insertNulls": false,
                "lineInterpolation": "smooth",
                "lineWidth": 1,
                "pointSize": 5,
                "scaleDistribution": {
                  "type": "linear"
                },
                "showPoints": "auto",
                "spanNulls": false,
                "stacking": {
                  "group": "A",
                  "mode": "none"
                },
                "thresholdsStyle": {
                  "mode": "off"
                }
              },
              "mappings": [],
              "thresholds": {
                "mode": "absolute",
                "steps": [
                  {
                    "color": "green"
                  },
                  {
                    "color": "red",
                    "value": 80
                  }
                ]
              },
              "unit": "ms"
            },
            "overrides": []
          },
          "gridPos": {
            "h": 12,
            "w": 24,
            "x": 0,
            "y": 49
          },
          "id": 16,
          "interval": "5m",
          "options": {
            "legend": {
              "calcs": [
                "mean",
                "logmin",
                "max",
                "delta"
              ],
              "displayMode": "table",
              "placement": "bottom",
              "showLegend": true
            },
            "tooltip": {
              "mode": "single",
              "sort": "none"
            }
          },
          "pluginVersion": "11.3.0",
          "targets": [
            {
              "datasource": {
                "type": "prometheus",
                "uid": "webstore-metrics"
              },
              "editorMode": "code",
              "exemplar": true,
              "expr": "topk(7,sum by (span_name,service_name)(increase(traces_span_metrics_duration_milliseconds_sum{service_name=~\"$service\", span_name=~\"$span_name\"}[$__rate_interval]) / increase(traces_span_metrics_duration_milliseconds_count{service_name=~\"$service\", span_name=~\"$span_name\"}[$__rate_interval])))",
              "instant": false,
              "interval": "",
              "legendFormat": "[{{service_name}}]  {{span_name}}",
              "range": true,
              "refId": "A"
            }
          ],
          "title": "Top 7 Latencies Over Range ",
          "type": "timeseries"
        }
      ],
      "preload": false,
      "refresh": "5m",
      "schemaVersion": 40,
      "tags": [],
      "templating": {
        "list": [
          {
            "allValue": ".*",
            "current": {
              "text": "All",
              "value": "$__all"
            },
            "datasource": {
              "type": "prometheus",
              "uid": "webstore-metrics"
            },
            "definition": "query_result(count by (service_name)(count_over_time(traces_span_metrics_calls_total[$__range])))",
            "includeAll": true,
            "multi": true,
            "name": "service",
            "options": [],
            "query": {
              "query": "query_result(count by (service_name)(count_over_time(traces_span_metrics_calls_total[$__range])))",
              "refId": "StandardVariableQuery"
            },
            "refresh": 2,
            "regex": "/.*service_name=\"(.*)\".*/",
            "sort": 1,
            "type": "query"
          },
          {
            "allValue": ".*",
            "current": {
              "text": "All",
              "value": "$__all"
            },
            "datasource": {
              "type": "prometheus",
              "uid": "webstore-metrics"
            },
            "definition": "query_result(sum ({__name__=~\".*traces_span_metrics_calls_total\",service_name=~\"$service\"})  by (span_name))",
            "includeAll": true,
            "multi": true,
            "name": "span_name",
            "options": [],
            "query": {
              "query": "query_result(sum ({__name__=~\".*traces_span_metrics_calls_total\",service_name=~\"$service\"})  by (span_name))",
              "refId": "StandardVariableQuery"
            },
            "refresh": 2,
            "regex": "/.*span_name=\"(.*)\".*/",
            "type": "query"
          }
        ]
      },
      "time": {
        "from": "now-15m",
        "to": "now"
      },
      "timepicker": {},
      "timezone": "",
      "title": "Spanmetrics Demo Dashboard",
      "uid": "W2gX2zHVk48",
      "version": 2,
      "weekStart": ""
    }
---
# Source: opentelemetry-demo/templates/product-catalog-products.yaml
apiVersion: v1
kind: ConfigMap
metadata:
  name: product-catalog-products
  namespace: otel-demo
  labels:
    
    app.kubernetes.io/instance: opentelemetry-demo
    app.kubernetes.io/version: "2.0.2"
    app.kubernetes.io/part-of: opentelemetry-demo
data:
  
  products.json: |
    {
      "products": [
        {
          "id": "OLJCESPC7Z",
          "name": "National Park Foundation Explorascope",
          "description": "The National Park Foundation’s (NPF) Explorascope 60AZ is a manual alt-azimuth, refractor telescope perfect for celestial viewing on the go. The NPF Explorascope 60 can view the planets, moon, star clusters and brighter deep sky objects like the Orion Nebula and Andromeda Galaxy.",
          "picture": "NationalParkFoundationExplorascope.jpg",
          "priceUsd": {
            "currencyCode": "USD",
            "units": 101,
            "nanos": 960000000
          },
          "categories": [
            "telescopes"
          ]
        },
        {
          "id": "66VCHSJNUP",
          "name": "Starsense Explorer Refractor Telescope",
          "description": "The first telescope that uses your smartphone to analyze the night sky and calculate its position in real time. StarSense Explorer is ideal for beginners thanks to the app’s user-friendly interface and detailed tutorials. It’s like having your own personal tour guide of the night sky",
          "picture": "StarsenseExplorer.jpg",
          "priceUsd": {
            "currencyCode": "USD",
            "units": 349,
            "nanos": *********
          },
          "categories": [
            "telescopes"
          ]
        },
        {
          "id": "1YMWWN1N4O",
          "name": "Eclipsmart Travel Refractor Telescope",
          "description": "Dedicated white-light solar scope for the observer on the go. The 50mm refracting solar scope uses Solar Safe, ISO compliant, full-aperture glass filter material to ensure the safest view of solar events.  The kit comes complete with everything you need, including the dedicated travel solar scope, a Solar Safe finderscope, tripod, a high quality 20mm (18x) Kellner eyepiece and a nylon backpack to carry everything in.  This Travel Solar Scope makes it easy to share the Sun as well as partial and total solar eclipses with the whole family and offers much higher magnifications than you would otherwise get using handheld solar viewers or binoculars.",
          "picture": "EclipsmartTravelRefractorTelescope.jpg",
          "priceUsd": {
            "currencyCode": "USD",
            "units": 129,
            "nanos": *********
          },
          "categories": [
            "telescopes",
            "travel"
          ]
        },
        {
          "id": "L9ECAV7KIM",
          "name": "Lens Cleaning Kit",
          "description": "Wipe away dust, dirt, fingerprints and other particles on your lenses to see clearly with the Lens Cleaning Kit. This cleaning kit works on all glass and optical surfaces, including telescopes, binoculars, spotting scopes, monoculars, microscopes, and even your camera lenses, computer screens, and mobile devices.  The kit comes complete with a retractable lens brush to remove dust particles and dirt and two options to clean smudges and fingerprints off of your optics, pre-moistened lens wipes and a bottled lens cleaning fluid with soft cloth.",
          "picture": "LensCleaningKit.jpg",
          "priceUsd": {
            "currencyCode": "USD",
            "units": 21,
            "nanos": *********
          },
          "categories": [
            "accessories"
          ]
        },
        {
          "id": "2ZYFJ3GM2N",
          "name": "Roof Binoculars",
          "description": "This versatile, all-around binocular is a great choice for the trail, the stadium, the arena, or just about anywhere you want a close-up view of the action without sacrificing brightness or detail. It’s an especially great companion for nature observation and bird watching, with ED glass that helps you spot the subtlest field markings and a close focus of just 6.5 feet.",
          "picture": "RoofBinoculars.jpg",
          "priceUsd": {
            "currencyCode": "USD",
            "units": 209,
            "nanos": *********
          },
          "categories": [
            "binoculars"
          ]
        },
        {
          "id": "0PUK6V6EV0",
          "name": "Solar System Color Imager",
          "description": "You have your new telescope and have observed Saturn and Jupiter. Now you're ready to take the next step and start imaging them. But where do you begin? The NexImage 10 Solar System Imager is the perfect solution.",
          "picture": "SolarSystemColorImager.jpg",
          "priceUsd": {
            "currencyCode": "USD",
            "units": 175,
            "nanos": 0
          },
          "categories": [
            "accessories",
            "telescopes"
          ]
        },
        {
          "id": "LS4PSXUNUM",
          "name": "Red Flashlight",
          "description": "This 3-in-1 device features a 3-mode red flashlight, a hand warmer, and a portable power bank for recharging your personal electronics on the go. Whether you use it to light the way at an astronomy star party, a night walk, or wildlife research, ThermoTorch 3 Astro Red’s rugged, IPX4-rated design will withstand your everyday activities.",
          "picture": "RedFlashlight.jpg",
          "priceUsd": {
            "currencyCode": "USD",
            "units": 57,
            "nanos": ********
          },
          "categories": [
            "accessories",
            "flashlights"
          ]
        },
        {
          "id": "9SIQT8TOJO",
          "name": "Optical Tube Assembly",
          "description": "Capturing impressive deep-sky astroimages is easier than ever with Rowe-Ackermann Schmidt Astrograph (RASA) V2, the perfect companion to today’s top DSLR or astronomical CCD cameras. This fast, wide-field f/2.2 system allows for shorter exposure times compared to traditional f/10 astroimaging, without sacrificing resolution. Because shorter sub-exposure times are possible, your equatorial mount won’t need to accurately track over extended periods. The short focal length also lessens equatorial tracking demands. In many cases, autoguiding will not be required.",
          "picture": "OpticalTubeAssembly.jpg",
          "priceUsd": {
            "currencyCode": "USD",
            "units": 3599,
            "nanos": 0
          },
          "categories": [
            "accessories",
            "telescopes",
            "assembly"
          ]
        },
        {
          "id": "6E92ZMYYFZ",
          "name": "Solar Filter",
          "description": "Enhance your viewing experience with EclipSmart Solar Filter for 8” telescopes. With two Velcro straps and four self-adhesive Velcro pads for added safety, you can be assured that the solar filter cannot be accidentally knocked off and will provide Solar Safe, ISO compliant viewing.",
          "picture": "SolarFilter.jpg",
          "priceUsd": {
            "currencyCode": "USD",
            "units": 69,
            "nanos": *********
          },
          "categories": [
            "accessories",
            "telescopes"
          ]
        },
        {
          "id": "HQTGWGPNH4",
          "name": "The Comet Book",
          "description": "A 16th-century treatise on comets, created anonymously in Flanders (now northern France) and now held at the Universitätsbibliothek Kassel. Commonly known as The Comet Book (or Kometenbuch in German), its full title translates as “Comets and their General and Particular Meanings, According to Ptolomeé, Albumasar, Haly, Aliquind and other Astrologers”. The image is from https://publicdomainreview.org/collection/the-comet-book, made available by the Universitätsbibliothek Kassel under a CC-BY SA 4.0 license (https://creativecommons.org/licenses/by-sa/4.0/)",
          "picture": "TheCometBook.jpg",
          "priceUsd": {
            "currencyCode": "USD",
            "units": 0,
            "nanos": 990000000
          },
          "categories": [
            "books"
          ]
        }
      ]
    }
---
# Source: opentelemetry-demo/charts/grafana/templates/clusterrole.yaml
kind: ClusterRole
apiVersion: rbac.authorization.k8s.io/v1
metadata:
  labels:
    app.kubernetes.io/name: grafana
    app.kubernetes.io/instance: opentelemetry-demo
    app.kubernetes.io/version: "11.5.2"
  name: grafana-clusterrole
rules: []
---
# Source: opentelemetry-demo/charts/opentelemetry-collector/templates/clusterrole.yaml
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRole
metadata:
  name: otel-collector
  labels:
    app.kubernetes.io/name: opentelemetry-collector
    app.kubernetes.io/instance: opentelemetry-demo
    app.kubernetes.io/version: "0.120.0"
    app.kubernetes.io/component: standalone-collector
rules:
  - apiGroups: [""]
    resources: ["pods", "namespaces"]
    verbs: ["get", "watch", "list"]
  - apiGroups: ["apps"]
    resources: ["replicasets"]
    verbs: ["get", "list", "watch"]
  - apiGroups: ["extensions"]
    resources: ["replicasets"]
    verbs: ["get", "list", "watch"]
---
# Source: opentelemetry-demo/charts/prometheus/templates/clusterrole.yaml
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRole
metadata:
  labels:
    app.kubernetes.io/component: server
    app.kubernetes.io/name: prometheus
    app.kubernetes.io/instance: opentelemetry-demo
    app.kubernetes.io/version: v3.1.0
    app.kubernetes.io/part-of: prometheus
  name: prometheus
rules:
  - apiGroups:
      - ""
    resources:
      - nodes
      - nodes/proxy
      - nodes/metrics
      - services
      - endpoints
      - pods
      - ingresses
      - configmaps
    verbs:
      - get
      - list
      - watch
  - apiGroups:
      - "extensions"
      - "networking.k8s.io"
    resources:
      - ingresses/status
      - ingresses
    verbs:
      - get
      - list
      - watch
  - apiGroups:
      - "discovery.k8s.io"
    resources:
      - endpointslices
    verbs:
      - get
      - list
      - watch
  - nonResourceURLs:
      - "/metrics"
    verbs:
      - get
---
# Source: opentelemetry-demo/charts/grafana/templates/clusterrolebinding.yaml
kind: ClusterRoleBinding
apiVersion: rbac.authorization.k8s.io/v1
metadata:
  name: grafana-clusterrolebinding
  labels:
    app.kubernetes.io/name: grafana
    app.kubernetes.io/instance: opentelemetry-demo
    app.kubernetes.io/version: "11.5.2"
subjects:
  - kind: ServiceAccount
    name: grafana
    namespace: otel-demo
roleRef:
  kind: ClusterRole
  name: grafana-clusterrole
  apiGroup: rbac.authorization.k8s.io
---
# Source: opentelemetry-demo/charts/opentelemetry-collector/templates/clusterrolebinding.yaml
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRoleBinding
metadata:
  name: otel-collector
  labels:
    app.kubernetes.io/name: opentelemetry-collector
    app.kubernetes.io/instance: opentelemetry-demo
    app.kubernetes.io/version: "0.120.0"
    app.kubernetes.io/component: standalone-collector
roleRef:
  apiGroup: rbac.authorization.k8s.io
  kind: ClusterRole
  name: otel-collector
subjects:
- kind: ServiceAccount
  name: otel-collector
  namespace: otel-demo
---
# Source: opentelemetry-demo/charts/prometheus/templates/clusterrolebinding.yaml
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRoleBinding
metadata:
  labels:
    app.kubernetes.io/component: server
    app.kubernetes.io/name: prometheus
    app.kubernetes.io/instance: opentelemetry-demo
    app.kubernetes.io/version: v3.1.0
    app.kubernetes.io/part-of: prometheus
  name: prometheus
subjects:
  - kind: ServiceAccount
    name: prometheus
    namespace: otel-demo
roleRef:
  apiGroup: rbac.authorization.k8s.io
  kind: ClusterRole
  name: prometheus
---
# Source: opentelemetry-demo/charts/grafana/templates/role.yaml
apiVersion: rbac.authorization.k8s.io/v1
kind: Role
metadata:
  name: grafana
  namespace: otel-demo
  labels:
    app.kubernetes.io/name: grafana
    app.kubernetes.io/instance: opentelemetry-demo
    app.kubernetes.io/version: "11.5.2"
rules: []
---
# Source: opentelemetry-demo/charts/grafana/templates/rolebinding.yaml
apiVersion: rbac.authorization.k8s.io/v1
kind: RoleBinding
metadata:
  name: grafana
  namespace: otel-demo
  labels:
    app.kubernetes.io/name: grafana
    app.kubernetes.io/instance: opentelemetry-demo
    app.kubernetes.io/version: "11.5.2"
roleRef:
  apiGroup: rbac.authorization.k8s.io
  kind: Role
  name: grafana
subjects:
- kind: ServiceAccount
  name: grafana
  namespace: otel-demo
---
# Source: opentelemetry-demo/charts/grafana/templates/service.yaml
apiVersion: v1
kind: Service
metadata:
  name: grafana
  namespace: otel-demo
  labels:
    app.kubernetes.io/name: grafana
    app.kubernetes.io/instance: opentelemetry-demo
    app.kubernetes.io/version: "11.5.2"
spec:
  type: ClusterIP
  ports:
    - name: service
      port: 80
      protocol: TCP
      targetPort: 3000
  selector:
    app.kubernetes.io/name: grafana
    app.kubernetes.io/instance: opentelemetry-demo
---
# Source: opentelemetry-demo/charts/jaeger/templates/allinone-agent-svc.yaml
apiVersion: v1
kind: Service
metadata:
  name: jaeger-agent
  labels:
    app.kubernetes.io/name: jaeger
    app.kubernetes.io/instance: opentelemetry-demo
    app.kubernetes.io/version: "1.53.0"
    app.kubernetes.io/component: service-agent
spec:
  clusterIP: None
  ports:
    - name: zk-compact-trft
      port: 5775
      protocol: UDP
      targetPort: 0
    - name: config-rest
      port: 5778
      targetPort: 0
    - name: jg-compact-trft
      port: 6831
      protocol: UDP
      targetPort: 0
    - name: jg-binary-trft
      port: 6832
      protocol: UDP
      targetPort: 0
  selector:
    app.kubernetes.io/name: jaeger
    app.kubernetes.io/instance: opentelemetry-demo
    app.kubernetes.io/component: all-in-one
---
# Source: opentelemetry-demo/charts/jaeger/templates/allinone-collector-svc.yaml
apiVersion: v1
kind: Service
metadata:
  name: jaeger-collector
  labels:
    app.kubernetes.io/name: jaeger
    app.kubernetes.io/instance: opentelemetry-demo
    app.kubernetes.io/version: "1.53.0"
    app.kubernetes.io/component: service-collector
spec:
  clusterIP: None
  ports:
    - name: http-zipkin
      port: 9411
      targetPort: 0
      appProtocol: http
    - name: grpc-http
      port: 14250
      targetPort: 0
      appProtocol: grpc
    - name: c-tchan-trft
      port: 14267
      targetPort: 0
    - name: http-c-binary-trft
      port: 14268
      targetPort: 0
      appProtocol: http
    - name: otlp-grpc
      port: 4317
      targetPort: 0
      appProtocol: grpc
    - name: otlp-http
      port: 4318
      targetPort: 0
      appProtocol: http
  selector:
    app.kubernetes.io/name: jaeger
    app.kubernetes.io/instance: opentelemetry-demo
    app.kubernetes.io/component: all-in-one
---
# Source: opentelemetry-demo/charts/jaeger/templates/allinone-query-svc.yaml
apiVersion: v1
kind: Service
metadata:
  name: jaeger-query
  labels:
    app.kubernetes.io/name: jaeger
    app.kubernetes.io/instance: opentelemetry-demo
    app.kubernetes.io/version: "1.53.0"
    app.kubernetes.io/component: service-query
spec:
  clusterIP: None
  ports:
    - name: http-query
      port: 16686
      targetPort: 16686
    - name: grpc-query
      port: 16685
      targetPort: 16685
  selector:
    app.kubernetes.io/name: jaeger
    app.kubernetes.io/instance: opentelemetry-demo
    app.kubernetes.io/component: all-in-one
---
# Source: opentelemetry-demo/charts/opensearch/templates/service.yaml
kind: Service
apiVersion: v1
metadata:
  name: opensearch
  labels:
    app.kubernetes.io/name: opensearch
    app.kubernetes.io/instance: opentelemetry-demo
    app.kubernetes.io/version: "2.19.0"
    app.kubernetes.io/component: opensearch
  annotations:
    {}
spec:
  type: ClusterIP
  selector:
    app.kubernetes.io/name: opensearch
    app.kubernetes.io/instance: opentelemetry-demo
  ports:
  - name: http
    protocol: TCP
    port: 9200
  - name: transport
    protocol: TCP
    port: 9300
  - name: metrics
    protocol: TCP
    port: 9600
---
# Source: opentelemetry-demo/charts/opensearch/templates/service.yaml
kind: Service
apiVersion: v1
metadata:
  name: opensearch-headless
  labels:
    app.kubernetes.io/name: opensearch
    app.kubernetes.io/instance: opentelemetry-demo
    app.kubernetes.io/version: "2.19.0"
    app.kubernetes.io/component: opensearch
  annotations:
    service.alpha.kubernetes.io/tolerate-unready-endpoints: "true"
spec:
  clusterIP: None # This is needed for statefulset hostnames like opensearch-0 to resolve
  # Create endpoints also if the related pod isn't ready
  publishNotReadyAddresses: true
  selector:
    app.kubernetes.io/name: opensearch
    app.kubernetes.io/instance: opentelemetry-demo
  ports:
  - name: http
    port: 9200
  - name: transport
    port: 9300
  - name: metrics
    port: 9600
---
# Source: opentelemetry-demo/charts/opentelemetry-collector/templates/service.yaml
apiVersion: v1
kind: Service
metadata:
  name: otel-collector
  namespace: otel-demo
  labels:
    app.kubernetes.io/name: opentelemetry-collector
    app.kubernetes.io/instance: opentelemetry-demo
    app.kubernetes.io/version: "0.120.0"
    app.kubernetes.io/component: standalone-collector
    component: standalone-collector
spec:
  type: ClusterIP
  ports:
    
    - name: jaeger-compact
      port: 6831
      targetPort: 6831
      protocol: UDP
    - name: jaeger-grpc
      port: 14250
      targetPort: 14250
      protocol: TCP
    - name: jaeger-thrift
      port: 14268
      targetPort: 14268
      protocol: TCP
    - name: metrics
      port: 8888
      targetPort: 8888
      protocol: TCP
    - name: otlp
      port: 4317
      targetPort: 4317
      protocol: TCP
      appProtocol: grpc
    - name: otlp-http
      port: 4318
      targetPort: 4318
      protocol: TCP
    - name: zipkin
      port: 9411
      targetPort: 9411
      protocol: TCP
  selector:
    app.kubernetes.io/name: opentelemetry-collector
    app.kubernetes.io/instance: opentelemetry-demo
    component: standalone-collector
  internalTrafficPolicy: Cluster
---
# Source: opentelemetry-demo/charts/prometheus/templates/service.yaml
apiVersion: v1
kind: Service
metadata:
  labels:
    app.kubernetes.io/component: server
    app.kubernetes.io/name: prometheus
    app.kubernetes.io/instance: opentelemetry-demo
    app.kubernetes.io/version: v3.1.0
    app.kubernetes.io/part-of: prometheus
  name: prometheus
  namespace: otel-demo
spec:
  ports:
    - name: http
      port: 9090
      protocol: TCP
      targetPort: 9090
  selector:
    app.kubernetes.io/component: server
    app.kubernetes.io/name: prometheus
    app.kubernetes.io/instance: opentelemetry-demo
  sessionAffinity: None
  type: "ClusterIP"
---
# Source: opentelemetry-demo/templates/component.yaml
apiVersion: v1
kind: Service
metadata:
  name: ad
  labels:
    
    opentelemetry.io/name: ad
    app.kubernetes.io/instance: opentelemetry-demo
    app.kubernetes.io/component: ad
    app.kubernetes.io/name: ad
    app.kubernetes.io/version: "2.0.2"
    app.kubernetes.io/part-of: opentelemetry-demo
spec:
  type: ClusterIP
  ports:
    - port: 8080
      name: tcp-service
      targetPort: 8080
  selector:
    
    opentelemetry.io/name: ad
---
# Source: opentelemetry-demo/templates/component.yaml
apiVersion: v1
kind: Service
metadata:
  name: cart
  labels:
    
    opentelemetry.io/name: cart
    app.kubernetes.io/instance: opentelemetry-demo
    app.kubernetes.io/component: cart
    app.kubernetes.io/name: cart
    app.kubernetes.io/version: "2.0.2"
    app.kubernetes.io/part-of: opentelemetry-demo
spec:
  type: ClusterIP
  ports:
    - port: 8080
      name: tcp-service
      targetPort: 8080
  selector:
    
    opentelemetry.io/name: cart
---
# Source: opentelemetry-demo/templates/component.yaml
apiVersion: v1
kind: Service
metadata:
  name: checkout
  labels:
    
    opentelemetry.io/name: checkout
    app.kubernetes.io/instance: opentelemetry-demo
    app.kubernetes.io/component: checkout
    app.kubernetes.io/name: checkout
    app.kubernetes.io/version: "2.0.2"
    app.kubernetes.io/part-of: opentelemetry-demo
spec:
  type: ClusterIP
  ports:
    - port: 8080
      name: tcp-service
      targetPort: 8080
  selector:
    
    opentelemetry.io/name: checkout
---
# Source: opentelemetry-demo/templates/component.yaml
apiVersion: v1
kind: Service
metadata:
  name: currency
  labels:
    
    opentelemetry.io/name: currency
    app.kubernetes.io/instance: opentelemetry-demo
    app.kubernetes.io/component: currency
    app.kubernetes.io/name: currency
    app.kubernetes.io/version: "2.0.2"
    app.kubernetes.io/part-of: opentelemetry-demo
spec:
  type: ClusterIP
  ports:
    - port: 8080
      name: tcp-service
      targetPort: 8080
  selector:
    
    opentelemetry.io/name: currency
---
# Source: opentelemetry-demo/templates/component.yaml
apiVersion: v1
kind: Service
metadata:
  name: email
  labels:
    
    opentelemetry.io/name: email
    app.kubernetes.io/instance: opentelemetry-demo
    app.kubernetes.io/component: email
    app.kubernetes.io/name: email
    app.kubernetes.io/version: "2.0.2"
    app.kubernetes.io/part-of: opentelemetry-demo
spec:
  type: ClusterIP
  ports:
    - port: 8080
      name: tcp-service
      targetPort: 8080
  selector:
    
    opentelemetry.io/name: email
---
# Source: opentelemetry-demo/templates/component.yaml
apiVersion: v1
kind: Service
metadata:
  name: flagd
  labels:
    
    opentelemetry.io/name: flagd
    app.kubernetes.io/instance: opentelemetry-demo
    app.kubernetes.io/component: flagd
    app.kubernetes.io/name: flagd
    app.kubernetes.io/version: "2.0.2"
    app.kubernetes.io/part-of: opentelemetry-demo
spec:
  type: ClusterIP
  ports:
    - port: 8013
      name: rpc
      targetPort: 8013
    - port: 8016
      name: ofrep
      targetPort: 8016
    - port: 4000
      name: tcp-service-0
      targetPort: 4000
  selector:
    
    opentelemetry.io/name: flagd
---
# Source: opentelemetry-demo/templates/component.yaml
apiVersion: v1
kind: Service
metadata:
  name: frontend
  labels:
    
    opentelemetry.io/name: frontend
    app.kubernetes.io/instance: opentelemetry-demo
    app.kubernetes.io/component: frontend
    app.kubernetes.io/name: frontend
    app.kubernetes.io/version: "2.0.2"
    app.kubernetes.io/part-of: opentelemetry-demo
spec:
  type: ClusterIP
  ports:
    - port: 8080
      name: tcp-service
      targetPort: 8080
  selector:
    
    opentelemetry.io/name: frontend
---
# Source: opentelemetry-demo/templates/component.yaml
apiVersion: v1
kind: Service
metadata:
  name: frontend-proxy
  labels:
    
    opentelemetry.io/name: frontend-proxy
    app.kubernetes.io/instance: opentelemetry-demo
    app.kubernetes.io/component: frontend-proxy
    app.kubernetes.io/name: frontend-proxy
    app.kubernetes.io/version: "2.0.2"
    app.kubernetes.io/part-of: opentelemetry-demo
spec:
  type: ClusterIP
  ports:
    - port: 8080
      name: tcp-service
      targetPort: 8080
  selector:
    
    opentelemetry.io/name: frontend-proxy
---
# Source: opentelemetry-demo/templates/component.yaml
apiVersion: v1
kind: Service
metadata:
  name: image-provider
  labels:
    
    opentelemetry.io/name: image-provider
    app.kubernetes.io/instance: opentelemetry-demo
    app.kubernetes.io/component: image-provider
    app.kubernetes.io/name: image-provider
    app.kubernetes.io/version: "2.0.2"
    app.kubernetes.io/part-of: opentelemetry-demo
spec:
  type: ClusterIP
  ports:
    - port: 8081
      name: tcp-service
      targetPort: 8081
  selector:
    
    opentelemetry.io/name: image-provider
---
# Source: opentelemetry-demo/templates/component.yaml
apiVersion: v1
kind: Service
metadata:
  name: kafka
  labels:
    
    opentelemetry.io/name: kafka
    app.kubernetes.io/instance: opentelemetry-demo
    app.kubernetes.io/component: kafka
    app.kubernetes.io/name: kafka
    app.kubernetes.io/version: "2.0.2"
    app.kubernetes.io/part-of: opentelemetry-demo
spec:
  type: ClusterIP
  ports:
    - port: 9092
      name: plaintext
      targetPort: 9092
    - port: 9093
      name: controller
      targetPort: 9093
  selector:
    
    opentelemetry.io/name: kafka
---
# Source: opentelemetry-demo/templates/component.yaml
apiVersion: v1
kind: Service
metadata:
  name: load-generator
  labels:
    
    opentelemetry.io/name: load-generator
    app.kubernetes.io/instance: opentelemetry-demo
    app.kubernetes.io/component: load-generator
    app.kubernetes.io/name: load-generator
    app.kubernetes.io/version: "2.0.2"
    app.kubernetes.io/part-of: opentelemetry-demo
spec:
  type: ClusterIP
  ports:
    - port: 8089
      name: tcp-service
      targetPort: 8089
  selector:
    
    opentelemetry.io/name: load-generator
---
# Source: opentelemetry-demo/templates/component.yaml
apiVersion: v1
kind: Service
metadata:
  name: payment
  labels:
    
    opentelemetry.io/name: payment
    app.kubernetes.io/instance: opentelemetry-demo
    app.kubernetes.io/component: payment
    app.kubernetes.io/name: payment
    app.kubernetes.io/version: "2.0.2"
    app.kubernetes.io/part-of: opentelemetry-demo
spec:
  type: ClusterIP
  ports:
    - port: 8080
      name: tcp-service
      targetPort: 8080
  selector:
    
    opentelemetry.io/name: payment
---
# Source: opentelemetry-demo/templates/component.yaml
apiVersion: v1
kind: Service
metadata:
  name: product-catalog
  labels:
    
    opentelemetry.io/name: product-catalog
    app.kubernetes.io/instance: opentelemetry-demo
    app.kubernetes.io/component: product-catalog
    app.kubernetes.io/name: product-catalog
    app.kubernetes.io/version: "2.0.2"
    app.kubernetes.io/part-of: opentelemetry-demo
spec:
  type: ClusterIP
  ports:
    - port: 8080
      name: tcp-service
      targetPort: 8080
  selector:
    
    opentelemetry.io/name: product-catalog
---
# Source: opentelemetry-demo/templates/component.yaml
apiVersion: v1
kind: Service
metadata:
  name: quote
  labels:
    
    opentelemetry.io/name: quote
    app.kubernetes.io/instance: opentelemetry-demo
    app.kubernetes.io/component: quote
    app.kubernetes.io/name: quote
    app.kubernetes.io/version: "2.0.2"
    app.kubernetes.io/part-of: opentelemetry-demo
spec:
  type: ClusterIP
  ports:
    - port: 8080
      name: tcp-service
      targetPort: 8080
  selector:
    
    opentelemetry.io/name: quote
---
# Source: opentelemetry-demo/templates/component.yaml
apiVersion: v1
kind: Service
metadata:
  name: recommendation
  labels:
    
    opentelemetry.io/name: recommendation
    app.kubernetes.io/instance: opentelemetry-demo
    app.kubernetes.io/component: recommendation
    app.kubernetes.io/name: recommendation
    app.kubernetes.io/version: "2.0.2"
    app.kubernetes.io/part-of: opentelemetry-demo
spec:
  type: ClusterIP
  ports:
    - port: 8080
      name: tcp-service
      targetPort: 8080
  selector:
    
    opentelemetry.io/name: recommendation
---
# Source: opentelemetry-demo/templates/component.yaml
apiVersion: v1
kind: Service
metadata:
  name: shipping
  labels:
    
    opentelemetry.io/name: shipping
    app.kubernetes.io/instance: opentelemetry-demo
    app.kubernetes.io/component: shipping
    app.kubernetes.io/name: shipping
    app.kubernetes.io/version: "2.0.2"
    app.kubernetes.io/part-of: opentelemetry-demo
spec:
  type: ClusterIP
  ports:
    - port: 8080
      name: tcp-service
      targetPort: 8080
  selector:
    
    opentelemetry.io/name: shipping
---
# Source: opentelemetry-demo/templates/component.yaml
apiVersion: v1
kind: Service
metadata:
  name: valkey-cart
  labels:
    
    opentelemetry.io/name: valkey-cart
    app.kubernetes.io/instance: opentelemetry-demo
    app.kubernetes.io/component: valkey-cart
    app.kubernetes.io/name: valkey-cart
    app.kubernetes.io/version: "2.0.2"
    app.kubernetes.io/part-of: opentelemetry-demo
spec:
  type: ClusterIP
  ports:
    - port: 6379
      name: valkey-cart
      targetPort: 6379
  selector:
    
    opentelemetry.io/name: valkey-cart
---
# Source: opentelemetry-demo/charts/grafana/templates/deployment.yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: grafana
  namespace: otel-demo
  labels:
    app.kubernetes.io/name: grafana
    app.kubernetes.io/instance: opentelemetry-demo
    app.kubernetes.io/version: "11.5.2"
spec:
  replicas: 1
  revisionHistoryLimit: 10
  selector:
    matchLabels:
      app.kubernetes.io/name: grafana
      app.kubernetes.io/instance: opentelemetry-demo
  strategy:
    type: RollingUpdate
  template:
    metadata:
      labels:
        app.kubernetes.io/name: grafana
        app.kubernetes.io/instance: opentelemetry-demo
        app.kubernetes.io/version: "11.5.2"
      annotations:
        checksum/config: 99cca986c6d5f6511900d815ee5a70d0c284aeb70af56fb96108c7bf456eff87
        checksum/sc-dashboard-provider-config: e70bf6a851099d385178a76de9757bb0bef8299da6d8443602590e44f05fdf24
        checksum/secret: bed677784356b2af7fb0d87455db21f077853059b594101a4f6532bfbd962a7f
        kubectl.kubernetes.io/default-container: grafana
    spec:
      
      serviceAccountName: grafana
      automountServiceAccountToken: true
      shareProcessNamespace: false
      securityContext:
        fsGroup: 472
        runAsGroup: 472
        runAsNonRoot: true
        runAsUser: 472
      enableServiceLinks: true
      containers:
        - name: grafana
          image: "swr.cn-north-4.myhuaweicloud.com/ddn-k8s/docker.io/grafana/grafana:11.5.2"
          imagePullPolicy: IfNotPresent
          securityContext:
            allowPrivilegeEscalation: false
            capabilities:
              drop:
              - ALL
            seccompProfile:
              type: RuntimeDefault
          volumeMounts:
            - name: config
              mountPath: "/etc/grafana/grafana.ini"
              subPath: grafana.ini
            - name: storage
              mountPath: "/var/lib/grafana"
            - name: dashboards-default
              mountPath: "/var/lib/grafana/dashboards/default"
            - name: config
              mountPath: "/etc/grafana/provisioning/datasources/datasources.yaml"
              subPath: "datasources.yaml"
            - name: config
              mountPath: "/etc/grafana/provisioning/dashboards/dashboardproviders.yaml"
              subPath: "dashboardproviders.yaml"
          ports:
            - name: grafana
              containerPort: 3000
              protocol: TCP
            - name: gossip-tcp
              containerPort: 9094
              protocol: TCP
            - name: gossip-udp
              containerPort: 9094
              protocol: UDP
            - name: profiling
              containerPort: 6060
              protocol: TCP
          env:
            - name: POD_IP
              valueFrom:
                fieldRef:
                  fieldPath: status.podIP
            - name: GF_SECURITY_ADMIN_USER
              valueFrom:
                secretKeyRef:
                  name: grafana
                  key: admin-user
            - name: GF_SECURITY_ADMIN_PASSWORD
              valueFrom:
                secretKeyRef:
                  name: grafana
                  key: admin-password
            - name: GF_INSTALL_PLUGINS
              valueFrom:
                configMapKeyRef:
                  name: grafana
                  key: plugins
            - name: GF_PATHS_DATA
              value: /var/lib/grafana/
            - name: GF_PATHS_LOGS
              value: /var/log/grafana
            - name: GF_PATHS_PLUGINS
              value: /var/lib/grafana/plugins
            - name: GF_PATHS_PROVISIONING
              value: /etc/grafana/provisioning
          livenessProbe:
            failureThreshold: 10
            httpGet:
              path: /api/health
              port: 3000
            initialDelaySeconds: 60
            timeoutSeconds: 30
          readinessProbe:
            httpGet:
              path: /api/health
              port: 3000
          resources:
            limits:
              memory: 150Mi
      volumes:
        - name: config
          configMap:
            name: grafana
        - name: dashboards-default
          configMap:
            name: grafana-dashboards
        - name: storage
          emptyDir: {}
---
# Source: opentelemetry-demo/charts/jaeger/templates/allinone-deploy.yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: jaeger
  labels:
    app.kubernetes.io/name: jaeger
    app.kubernetes.io/instance: opentelemetry-demo
    app.kubernetes.io/version: "1.53.0"
    app.kubernetes.io/component: all-in-one
    prometheus.io/port: "14269"
    prometheus.io/scrape: "true"
spec:
  replicas: 1
  strategy:
    type: Recreate
  selector:
    matchLabels:
      app.kubernetes.io/name: jaeger
      app.kubernetes.io/instance: opentelemetry-demo
      app.kubernetes.io/component: all-in-one
  template:
    metadata:
      labels:
        app.kubernetes.io/name: jaeger
        app.kubernetes.io/instance: opentelemetry-demo
        app.kubernetes.io/component: all-in-one
      annotations:
        prometheus.io/port: "14269"
        prometheus.io/scrape: "true"
    spec:
      
      containers:
        - env:
            - name: METRICS_STORAGE_TYPE
              value: prometheus
            - name: COLLECTOR_OTLP_GRPC_HOST_PORT
              value: 0.0.0.0:4317
            - name: COLLECTOR_OTLP_HTTP_HOST_PORT
              value: 0.0.0.0:4318
            - name: SPAN_STORAGE_TYPE
              value: memory
            
            - name: COLLECTOR_ZIPKIN_HOST_PORT
              value: :9411
            - name: JAEGER_DISABLED
              value: "false"
            - name: COLLECTOR_OTLP_ENABLED
              value: "true"
          securityContext:
            {}
          image: swr.cn-north-4.myhuaweicloud.com/ddn-k8s/docker.io/jaegertracing/all-in-one:1.53.0
          imagePullPolicy: IfNotPresent
          name: jaeger
          args:
            - "--memory.max-traces=5000"
            - "--query.base-path=/jaeger/ui"
            - "--prometheus.server-url=http://prometheus:9090"
            - "--prometheus.query.normalize-calls=true"
            - "--prometheus.query.normalize-duration=true"
          ports:
            - containerPort: 5775
              protocol: UDP
            - containerPort: 6831
              protocol: UDP
            - containerPort: 6832
              protocol: UDP
            - containerPort: 5778
              protocol: TCP
            - containerPort: 16686
              protocol: TCP
            - containerPort: 16685
              protocol: TCP
            - containerPort: 9411
              protocol: TCP
            - containerPort: 4317
              protocol: TCP
            - containerPort: 4318
              protocol: TCP
          livenessProbe:
            failureThreshold: 5
            httpGet:
              path: /
              port: 14269
              scheme: HTTP
            initialDelaySeconds: 5
            periodSeconds: 15
            successThreshold: 1
            timeoutSeconds: 1
          readinessProbe:
            failureThreshold: 3
            httpGet:
              path: /
              port: 14269
              scheme: HTTP
            initialDelaySeconds: 1
            periodSeconds: 10
            successThreshold: 1
            timeoutSeconds: 1
          resources:
            limits:
              memory: 400Mi
          volumeMounts:
      securityContext:
        fsGroup: 10001
        runAsGroup: 10001
        runAsUser: 10001
      serviceAccountName: jaeger
      volumes:
---
# Source: opentelemetry-demo/charts/opentelemetry-collector/templates/deployment.yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: otel-collector
  namespace: otel-demo
  labels:
    app.kubernetes.io/name: opentelemetry-collector
    app.kubernetes.io/instance: opentelemetry-demo
    app.kubernetes.io/version: "0.120.0"
    app.kubernetes.io/component: standalone-collector
spec:
  replicas: 1
  revisionHistoryLimit: 10
  selector:
    matchLabels:
      app.kubernetes.io/name: opentelemetry-collector
      app.kubernetes.io/instance: opentelemetry-demo
      component: standalone-collector
  strategy:
    type: RollingUpdate
  template:
    metadata:
      annotations:
        checksum/config: a89266db0e62ae4711e3cef2ea43e19dac4d19232eb4bb06b05882a36f128110
        opentelemetry_community_demo: "true"
        prometheus.io/scrape: "true"
      labels:
        app.kubernetes.io/name: opentelemetry-collector
        app.kubernetes.io/instance: opentelemetry-demo
        component: standalone-collector
        
    spec:
      
      serviceAccountName: otel-collector
      securityContext:
        {}
      containers:
        - name: opentelemetry-collector
          args:
            - --config=/conf/relay.yaml
          securityContext:
            {}
          image: "swr.cn-north-4.myhuaweicloud.com/ddn-k8s/docker.io/otel/opentelemetry-collector-contrib:0.120.0"
          imagePullPolicy: IfNotPresent
          ports:
            
            - name: jaeger-compact
              containerPort: 6831
              protocol: UDP
            - name: jaeger-grpc
              containerPort: 14250
              protocol: TCP
            - name: jaeger-thrift
              containerPort: 14268
              protocol: TCP
            - name: metrics
              containerPort: 8888
              protocol: TCP
            - name: otlp
              containerPort: 4317
              protocol: TCP
            - name: otlp-http
              containerPort: 4318
              protocol: TCP
            - name: zipkin
              containerPort: 9411
              protocol: TCP
          env:
            - name: MY_POD_IP
              valueFrom:
                fieldRef:
                  apiVersion: v1
                  fieldPath: status.podIP
            - name: GOMEMLIMIT
              value: "160MiB"
          livenessProbe:
            httpGet:
              path: /
              port: 13133
          readinessProbe:
            httpGet:
              path: /
              port: 13133
          resources:
            limits:
              memory: 200Mi
          volumeMounts:
            - mountPath: /conf
              name: opentelemetry-collector-configmap
      volumes:
        - name: opentelemetry-collector-configmap
          configMap:
            name: otel-collector
            items:
              - key: relay
                path: relay.yaml
      hostNetwork: false
---
# Source: opentelemetry-demo/charts/prometheus/templates/deploy.yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  labels:
    app.kubernetes.io/component: server
    app.kubernetes.io/name: prometheus
    app.kubernetes.io/instance: opentelemetry-demo
    app.kubernetes.io/version: v3.1.0
    app.kubernetes.io/part-of: prometheus
  name: prometheus
  namespace: otel-demo
spec:
  selector:
    matchLabels:
      app.kubernetes.io/component: server
      app.kubernetes.io/name: prometheus
      app.kubernetes.io/instance: opentelemetry-demo
  replicas: 1
  revisionHistoryLimit: 10
  strategy:
    type: Recreate
    rollingUpdate: null
  template:
    metadata:
      labels:
        app.kubernetes.io/component: server
        app.kubernetes.io/name: prometheus
        app.kubernetes.io/instance: opentelemetry-demo
        app.kubernetes.io/version: v3.1.0
        app.kubernetes.io/part-of: prometheus
    spec:
      enableServiceLinks: true
      serviceAccountName: prometheus
      containers:

        - name: prometheus-server
          image: "swr.cn-north-4.myhuaweicloud.com/ddn-k8s/quay.io/prometheus/prometheus:v3.1.0"
          imagePullPolicy: "IfNotPresent"
          args:
            - --storage.tsdb.retention.time=15d
            - --config.file=/etc/config/prometheus.yml
            - --storage.tsdb.path=/data
            - --web.console.libraries=/etc/prometheus/console_libraries
            - --web.console.templates=/etc/prometheus/consoles
            - --enable-feature=exemplar-storage
            - --web.enable-otlp-receiver
          ports:
            - containerPort: 9090
          readinessProbe:
            httpGet:
              path: /-/ready
              port: 9090
              scheme: HTTP
            initialDelaySeconds: 30
            periodSeconds: 5
            timeoutSeconds: 4
            failureThreshold: 3
            successThreshold: 1
          livenessProbe:
            httpGet:
              path: /-/healthy
              port: 9090
              scheme: HTTP
            initialDelaySeconds: 30
            periodSeconds: 15
            timeoutSeconds: 10
            failureThreshold: 3
            successThreshold: 1
          resources:
            limits:
              memory: 300Mi
          volumeMounts:
            - name: config-volume
              mountPath: /etc/config
            - name: storage-volume
              mountPath: /data
              subPath: ""
      dnsPolicy: ClusterFirst
      securityContext:
        fsGroup: 65534
        runAsGroup: 65534
        runAsNonRoot: true
        runAsUser: 65534
      terminationGracePeriodSeconds: 300
      volumes:
        - name: config-volume
          configMap:
            name: prometheus
        - name: storage-volume
          emptyDir:
            {}
---
# Source: opentelemetry-demo/templates/component.yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: accounting
  labels:
    
    opentelemetry.io/name: accounting
    app.kubernetes.io/instance: opentelemetry-demo
    app.kubernetes.io/component: accounting
    app.kubernetes.io/name: accounting
    app.kubernetes.io/version: "2.0.2"
    app.kubernetes.io/part-of: opentelemetry-demo
spec:
  replicas: 1
  revisionHistoryLimit: 10
  selector:
    matchLabels:
      
      opentelemetry.io/name: accounting
  template:
    metadata:
      labels:
        
        opentelemetry.io/name: accounting
        app.kubernetes.io/instance: opentelemetry-demo
        app.kubernetes.io/component: accounting
        app.kubernetes.io/name: accounting
    spec:
      serviceAccountName: opentelemetry-demo
      containers:
        - name: accounting
          image: 'swr.cn-north-4.myhuaweicloud.com/ddn-k8s/ghcr.io/open-telemetry/demo:2.0.2-accounting'
          imagePullPolicy: IfNotPresent
          env:
            - name: OTEL_SERVICE_NAME
              valueFrom:
                fieldRef:
                  apiVersion: v1
                  fieldPath: metadata.labels['app.kubernetes.io/component']
            - name: OTEL_COLLECTOR_NAME
              value: otel-collector
            - name: OTEL_EXPORTER_OTLP_METRICS_TEMPORALITY_PREFERENCE
              value: cumulative
            - name: KAFKA_ADDR
              value: kafka:9092
            - name: OTEL_EXPORTER_OTLP_ENDPOINT
              value: http://$(OTEL_COLLECTOR_NAME):4318
            - name: OTEL_RESOURCE_ATTRIBUTES
              value: service.name=$(OTEL_SERVICE_NAME),service.namespace=opentelemetry-demo,service.version=2.0.2
          resources:
            limits:
              memory: 120Mi
          volumeMounts:
      initContainers:
        - command:
          - sh
          - -c
          - until nc -z -v -w30 kafka 9092; do echo waiting for kafka; sleep 2; done;
          image: swr.cn-north-4.myhuaweicloud.com/ddn-k8s/docker.io/library/busybox:latest
          name: wait-for-kafka
      volumes:
---
# Source: opentelemetry-demo/templates/component.yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: ad
  labels:
    
    opentelemetry.io/name: ad
    app.kubernetes.io/instance: opentelemetry-demo
    app.kubernetes.io/component: ad
    app.kubernetes.io/name: ad
    app.kubernetes.io/version: "2.0.2"
    app.kubernetes.io/part-of: opentelemetry-demo
spec:
  replicas: 1
  revisionHistoryLimit: 10
  selector:
    matchLabels:
      
      opentelemetry.io/name: ad
  template:
    metadata:
      labels:
        
        opentelemetry.io/name: ad
        app.kubernetes.io/instance: opentelemetry-demo
        app.kubernetes.io/component: ad
        app.kubernetes.io/name: ad
    spec:
      serviceAccountName: opentelemetry-demo
      containers:
        - name: ad
          image: 'swr.cn-north-4.myhuaweicloud.com/ddn-k8s/ghcr.io/open-telemetry/demo:2.0.2-ad'
          imagePullPolicy: IfNotPresent
          ports:
            
            - containerPort: 8080
              name: service
          env:
            - name: OTEL_SERVICE_NAME
              valueFrom:
                fieldRef:
                  apiVersion: v1
                  fieldPath: metadata.labels['app.kubernetes.io/component']
            - name: OTEL_COLLECTOR_NAME
              value: otel-collector
            - name: OTEL_EXPORTER_OTLP_METRICS_TEMPORALITY_PREFERENCE
              value: cumulative
            - name: AD_PORT
              value: "8080"
            - name: FLAGD_HOST
              value: flagd
            - name: FLAGD_PORT
              value: "8013"
            - name: OTEL_EXPORTER_OTLP_ENDPOINT
              value: http://$(OTEL_COLLECTOR_NAME):4318
            - name: OTEL_LOGS_EXPORTER
              value: otlp
            - name: OTEL_RESOURCE_ATTRIBUTES
              value: service.name=$(OTEL_SERVICE_NAME),service.namespace=opentelemetry-demo,service.version=2.0.2
          resources:
            limits:
              memory: 300Mi
          volumeMounts:
      volumes:
---
# Source: opentelemetry-demo/templates/component.yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: cart
  labels:
    
    opentelemetry.io/name: cart
    app.kubernetes.io/instance: opentelemetry-demo
    app.kubernetes.io/component: cart
    app.kubernetes.io/name: cart
    app.kubernetes.io/version: "2.0.2"
    app.kubernetes.io/part-of: opentelemetry-demo
spec:
  replicas: 1
  revisionHistoryLimit: 10
  selector:
    matchLabels:
      
      opentelemetry.io/name: cart
  template:
    metadata:
      labels:
        
        opentelemetry.io/name: cart
        app.kubernetes.io/instance: opentelemetry-demo
        app.kubernetes.io/component: cart
        app.kubernetes.io/name: cart
    spec:
      serviceAccountName: opentelemetry-demo
      containers:
        - name: cart
          image: 'swr.cn-north-4.myhuaweicloud.com/ddn-k8s/ghcr.io/open-telemetry/demo:2.0.2-cart'
          imagePullPolicy: IfNotPresent
          ports:
            
            - containerPort: 8080
              name: service
          env:
            - name: OTEL_SERVICE_NAME
              valueFrom:
                fieldRef:
                  apiVersion: v1
                  fieldPath: metadata.labels['app.kubernetes.io/component']
            - name: OTEL_COLLECTOR_NAME
              value: otel-collector
            - name: OTEL_EXPORTER_OTLP_METRICS_TEMPORALITY_PREFERENCE
              value: cumulative
            - name: CART_PORT
              value: "8080"
            - name: ASPNETCORE_URLS
              value: http://*:$(CART_PORT)
            - name: VALKEY_ADDR
              value: valkey-cart:6379
            - name: FLAGD_HOST
              value: flagd
            - name: FLAGD_PORT
              value: "8013"
            - name: OTEL_EXPORTER_OTLP_ENDPOINT
              value: http://$(OTEL_COLLECTOR_NAME):4317
            - name: OTEL_RESOURCE_ATTRIBUTES
              value: service.name=$(OTEL_SERVICE_NAME),service.namespace=opentelemetry-demo,service.version=2.0.2
          resources:
            limits:
              memory: 160Mi
          volumeMounts:
      initContainers:
        - command:
          - sh
          - -c
          - until nc -z -v -w30 valkey-cart 6379; do echo waiting for valkey-cart; sleep 2;
            done;
          image: swr.cn-north-4.myhuaweicloud.com/ddn-k8s/docker.io/library/busybox:latest
          name: wait-for-valkey-cart
      volumes:
---
# Source: opentelemetry-demo/templates/component.yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: checkout
  labels:
    
    opentelemetry.io/name: checkout
    app.kubernetes.io/instance: opentelemetry-demo
    app.kubernetes.io/component: checkout
    app.kubernetes.io/name: checkout
    app.kubernetes.io/version: "2.0.2"
    app.kubernetes.io/part-of: opentelemetry-demo
spec:
  replicas: 1
  revisionHistoryLimit: 10
  selector:
    matchLabels:
      
      opentelemetry.io/name: checkout
  template:
    metadata:
      labels:
        
        opentelemetry.io/name: checkout
        app.kubernetes.io/instance: opentelemetry-demo
        app.kubernetes.io/component: checkout
        app.kubernetes.io/name: checkout
    spec:
      serviceAccountName: opentelemetry-demo
      containers:
        - name: checkout
          image: 'swr.cn-north-4.myhuaweicloud.com/ddn-k8s/ghcr.io/open-telemetry/demo:2.0.2-checkout'
          imagePullPolicy: IfNotPresent
          ports:
            
            - containerPort: 8080
              name: service
          env:
            - name: OTEL_SERVICE_NAME
              valueFrom:
                fieldRef:
                  apiVersion: v1
                  fieldPath: metadata.labels['app.kubernetes.io/component']
            - name: OTEL_COLLECTOR_NAME
              value: otel-collector
            - name: OTEL_EXPORTER_OTLP_METRICS_TEMPORALITY_PREFERENCE
              value: cumulative
            - name: CHECKOUT_PORT
              value: "8080"
            - name: CART_ADDR
              value: cart:8080
            - name: CURRENCY_ADDR
              value: currency:8080
            - name: EMAIL_ADDR
              value: http://email:8080
            - name: PAYMENT_ADDR
              value: payment:8080
            - name: PRODUCT_CATALOG_ADDR
              value: product-catalog:8080
            - name: SHIPPING_ADDR
              value: shipping:8080
            - name: KAFKA_ADDR
              value: kafka:9092
            - name: FLAGD_HOST
              value: flagd
            - name: FLAGD_PORT
              value: "8013"
            - name: OTEL_EXPORTER_OTLP_ENDPOINT
              value: http://$(OTEL_COLLECTOR_NAME):4317
            - name: OTEL_RESOURCE_ATTRIBUTES
              value: service.name=$(OTEL_SERVICE_NAME),service.namespace=opentelemetry-demo,service.version=2.0.2
          resources:
            limits:
              memory: 20Mi
          volumeMounts:
      initContainers:
        - command:
          - sh
          - -c
          - until nc -z -v -w30 kafka 9092; do echo waiting for kafka; sleep 2; done;
          image: swr.cn-north-4.myhuaweicloud.com/ddn-k8s/docker.io/library/busybox:latest
          name: wait-for-kafka
      volumes:
---
# Source: opentelemetry-demo/templates/component.yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: currency
  labels:
    
    opentelemetry.io/name: currency
    app.kubernetes.io/instance: opentelemetry-demo
    app.kubernetes.io/component: currency
    app.kubernetes.io/name: currency
    app.kubernetes.io/version: "2.0.2"
    app.kubernetes.io/part-of: opentelemetry-demo
spec:
  replicas: 1
  revisionHistoryLimit: 10
  selector:
    matchLabels:
      
      opentelemetry.io/name: currency
  template:
    metadata:
      labels:
        
        opentelemetry.io/name: currency
        app.kubernetes.io/instance: opentelemetry-demo
        app.kubernetes.io/component: currency
        app.kubernetes.io/name: currency
    spec:
      serviceAccountName: opentelemetry-demo
      containers:
        - name: currency
          image: 'swr.cn-north-4.myhuaweicloud.com/ddn-k8s/ghcr.io/open-telemetry/demo:2.0.2-currency'
          imagePullPolicy: IfNotPresent
          ports:
            
            - containerPort: 8080
              name: service
          env:
            - name: OTEL_SERVICE_NAME
              valueFrom:
                fieldRef:
                  apiVersion: v1
                  fieldPath: metadata.labels['app.kubernetes.io/component']
            - name: OTEL_COLLECTOR_NAME
              value: otel-collector
            - name: OTEL_EXPORTER_OTLP_METRICS_TEMPORALITY_PREFERENCE
              value: cumulative
            - name: CURRENCY_PORT
              value: "8080"
            - name: OTEL_EXPORTER_OTLP_ENDPOINT
              value: http://$(OTEL_COLLECTOR_NAME):4317
            - name: VERSION
              value: '2.0.2'
            - name: OTEL_RESOURCE_ATTRIBUTES
              value: service.name=$(OTEL_SERVICE_NAME),service.namespace=opentelemetry-demo,service.version=2.0.2
          resources:
            limits:
              memory: 20Mi
          volumeMounts:
      volumes:
---
# Source: opentelemetry-demo/templates/component.yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: email
  labels:
    
    opentelemetry.io/name: email
    app.kubernetes.io/instance: opentelemetry-demo
    app.kubernetes.io/component: email
    app.kubernetes.io/name: email
    app.kubernetes.io/version: "2.0.2"
    app.kubernetes.io/part-of: opentelemetry-demo
spec:
  replicas: 1
  revisionHistoryLimit: 10
  selector:
    matchLabels:
      
      opentelemetry.io/name: email
  template:
    metadata:
      labels:
        
        opentelemetry.io/name: email
        app.kubernetes.io/instance: opentelemetry-demo
        app.kubernetes.io/component: email
        app.kubernetes.io/name: email
    spec:
      serviceAccountName: opentelemetry-demo
      containers:
        - name: email
          image: 'swr.cn-north-4.myhuaweicloud.com/ddn-k8s/ghcr.io/open-telemetry/demo:2.0.2-email'
          imagePullPolicy: IfNotPresent
          ports:
            
            - containerPort: 8080
              name: service
          env:
            - name: OTEL_SERVICE_NAME
              valueFrom:
                fieldRef:
                  apiVersion: v1
                  fieldPath: metadata.labels['app.kubernetes.io/component']
            - name: OTEL_COLLECTOR_NAME
              value: otel-collector
            - name: OTEL_EXPORTER_OTLP_METRICS_TEMPORALITY_PREFERENCE
              value: cumulative
            - name: EMAIL_PORT
              value: "8080"
            - name: APP_ENV
              value: production
            - name: OTEL_EXPORTER_OTLP_TRACES_ENDPOINT
              value: http://$(OTEL_COLLECTOR_NAME):4318/v1/traces
            - name: OTEL_RESOURCE_ATTRIBUTES
              value: service.name=$(OTEL_SERVICE_NAME),service.namespace=opentelemetry-demo,service.version=2.0.2
          resources:
            limits:
              memory: 100Mi
          volumeMounts:
      volumes:
---
# Source: opentelemetry-demo/templates/component.yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: flagd
  labels:
    
    opentelemetry.io/name: flagd
    app.kubernetes.io/instance: opentelemetry-demo
    app.kubernetes.io/component: flagd
    app.kubernetes.io/name: flagd
    app.kubernetes.io/version: "2.0.2"
    app.kubernetes.io/part-of: opentelemetry-demo
spec:
  replicas: 1
  revisionHistoryLimit: 10
  selector:
    matchLabels:
      
      opentelemetry.io/name: flagd
  template:
    metadata:
      labels:
        
        opentelemetry.io/name: flagd
        app.kubernetes.io/instance: opentelemetry-demo
        app.kubernetes.io/component: flagd
        app.kubernetes.io/name: flagd
    spec:
      serviceAccountName: opentelemetry-demo
      containers:
        - name: flagd
          image: 'swr.cn-north-4.myhuaweicloud.com/ddn-k8s/ghcr.io/open-feature/flagd:v0.11.1'
          imagePullPolicy: IfNotPresent
          command:
            - /flagd-build
            - start
            - --port
            - "8013"
            - --ofrep-port
            - "8016"
            - --uri
            - file:./etc/flagd/demo.flagd.json
          ports:
            
            - containerPort: 8013
              name: rpc
            - containerPort: 8016
              name: ofrep
          env:
            - name: OTEL_SERVICE_NAME
              valueFrom:
                fieldRef:
                  apiVersion: v1
                  fieldPath: metadata.labels['app.kubernetes.io/component']
            - name: OTEL_COLLECTOR_NAME
              value: otel-collector
            - name: OTEL_EXPORTER_OTLP_METRICS_TEMPORALITY_PREFERENCE
              value: cumulative
            - name: FLAGD_METRICS_EXPORTER
              value: otel
            - name: FLAGD_OTEL_COLLECTOR_URI
              value: $(OTEL_COLLECTOR_NAME):4317
            - name: OTEL_RESOURCE_ATTRIBUTES
              value: service.name=$(OTEL_SERVICE_NAME),service.namespace=opentelemetry-demo,service.version=2.0.2
          resources:
            limits:
              memory: 75Mi
          volumeMounts:
            - name: config-rw
              mountPath: /etc/flagd
        - name: flagd-ui
          image: 'swr.cn-north-4.myhuaweicloud.com/ddn-k8s/ghcr.io/open-telemetry/demo:2.0.2-flagd-ui'
          imagePullPolicy: IfNotPresent
          ports:
            
            - containerPort: 4000
              name: service
          env:
            - name: OTEL_SERVICE_NAME
              valueFrom:
                fieldRef:
                  apiVersion: v1
                  fieldPath: metadata.labels['app.kubernetes.io/component']
            - name: OTEL_COLLECTOR_NAME
              value: otel-collector
            - name: OTEL_EXPORTER_OTLP_METRICS_TEMPORALITY_PREFERENCE
              value: cumulative
            - name: FLAGD_METRICS_EXPORTER
              value: otel
            - name: OTEL_EXPORTER_OTLP_ENDPOINT
              value: http://$(OTEL_COLLECTOR_NAME):4318
            - name: OTEL_RESOURCE_ATTRIBUTES
              value: service.name=$(OTEL_SERVICE_NAME),service.namespace=opentelemetry-demo,service.version=2.0.2
          resources:
            limits:
              memory: 100Mi
          volumeMounts:
            - mountPath: /app/data
              name: config-rw
      initContainers:
        - command:
          - sh
          - -c
          - cp /config-ro/demo.flagd.json /config-rw/demo.flagd.json && cat /config-rw/demo.flagd.json
          image: swr.cn-north-4.myhuaweicloud.com/ddn-k8s/docker.io/library/busybox:latest
          name: init-config
          volumeMounts:
          - mountPath: /config-ro
            name: config-ro
          - mountPath: /config-rw
            name: config-rw
      volumes:
        - name: config-rw
          emptyDir: {}
        - configMap:
            name: flagd-config
          name: config-ro
---
# Source: opentelemetry-demo/templates/component.yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: fraud-detection
  labels:
    
    opentelemetry.io/name: fraud-detection
    app.kubernetes.io/instance: opentelemetry-demo
    app.kubernetes.io/component: fraud-detection
    app.kubernetes.io/name: fraud-detection
    app.kubernetes.io/version: "2.0.2"
    app.kubernetes.io/part-of: opentelemetry-demo
spec:
  replicas: 1
  revisionHistoryLimit: 10
  selector:
    matchLabels:
      
      opentelemetry.io/name: fraud-detection
  template:
    metadata:
      labels:
        
        opentelemetry.io/name: fraud-detection
        app.kubernetes.io/instance: opentelemetry-demo
        app.kubernetes.io/component: fraud-detection
        app.kubernetes.io/name: fraud-detection
    spec:
      serviceAccountName: opentelemetry-demo
      containers:
        - name: fraud-detection
          image: 'swr.cn-north-4.myhuaweicloud.com/ddn-k8s/ghcr.io/open-telemetry/demo:2.0.2-fraud-detection'
          imagePullPolicy: IfNotPresent
          env:
            - name: OTEL_SERVICE_NAME
              valueFrom:
                fieldRef:
                  apiVersion: v1
                  fieldPath: metadata.labels['app.kubernetes.io/component']
            - name: OTEL_COLLECTOR_NAME
              value: otel-collector
            - name: OTEL_EXPORTER_OTLP_METRICS_TEMPORALITY_PREFERENCE
              value: cumulative
            - name: KAFKA_ADDR
              value: kafka:9092
            - name: FLAGD_HOST
              value: flagd
            - name: FLAGD_PORT
              value: "8013"
            - name: OTEL_EXPORTER_OTLP_ENDPOINT
              value: http://$(OTEL_COLLECTOR_NAME):4318
            - name: OTEL_RESOURCE_ATTRIBUTES
              value: service.name=$(OTEL_SERVICE_NAME),service.namespace=opentelemetry-demo,service.version=2.0.2
          resources:
            limits:
              memory: 300Mi
          volumeMounts:
      initContainers:
        - command:
          - sh
          - -c
          - until nc -z -v -w30 kafka 9092; do echo waiting for kafka; sleep 2; done;
          image: swr.cn-north-4.myhuaweicloud.com/ddn-k8s/docker.io/library/busybox:latest
          name: wait-for-kafka
      volumes:
---
# Source: opentelemetry-demo/templates/component.yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: frontend
  labels:
    
    opentelemetry.io/name: frontend
    app.kubernetes.io/instance: opentelemetry-demo
    app.kubernetes.io/component: frontend
    app.kubernetes.io/name: frontend
    app.kubernetes.io/version: "2.0.2"
    app.kubernetes.io/part-of: opentelemetry-demo
spec:
  replicas: 1
  revisionHistoryLimit: 10
  selector:
    matchLabels:
      
      opentelemetry.io/name: frontend
  template:
    metadata:
      labels:
        
        opentelemetry.io/name: frontend
        app.kubernetes.io/instance: opentelemetry-demo
        app.kubernetes.io/component: frontend
        app.kubernetes.io/name: frontend
    spec:
      serviceAccountName: opentelemetry-demo
      containers:
        - name: frontend
          image: 'swr.cn-north-4.myhuaweicloud.com/ddn-k8s/ghcr.io/open-telemetry/demo:2.0.2-frontend'
          imagePullPolicy: IfNotPresent
          ports:
            
            - containerPort: 8080
              name: service
          env:
            - name: OTEL_SERVICE_NAME
              valueFrom:
                fieldRef:
                  apiVersion: v1
                  fieldPath: metadata.labels['app.kubernetes.io/component']
            - name: OTEL_COLLECTOR_NAME
              value: otel-collector
            - name: OTEL_EXPORTER_OTLP_METRICS_TEMPORALITY_PREFERENCE
              value: cumulative
            - name: FRONTEND_PORT
              value: "8080"
            - name: FRONTEND_ADDR
              value: :8080
            - name: AD_ADDR
              value: ad:8080
            - name: CART_ADDR
              value: cart:8080
            - name: CHECKOUT_ADDR
              value: checkout:8080
            - name: CURRENCY_ADDR
              value: currency:8080
            - name: PRODUCT_CATALOG_ADDR
              value: product-catalog:8080
            - name: RECOMMENDATION_ADDR
              value: recommendation:8080
            - name: SHIPPING_ADDR
              value: shipping:8080
            - name: FLAGD_HOST
              value: flagd
            - name: FLAGD_PORT
              value: "8013"
            - name: OTEL_COLLECTOR_HOST
              value: $(OTEL_COLLECTOR_NAME)
            - name: OTEL_EXPORTER_OTLP_ENDPOINT
              value: http://$(OTEL_COLLECTOR_NAME):4317
            - name: WEB_OTEL_SERVICE_NAME
              value: frontend-web
            - name: PUBLIC_OTEL_EXPORTER_OTLP_TRACES_ENDPOINT
              value: http://localhost:8080/otlp-http/v1/traces
            - name: OTEL_RESOURCE_ATTRIBUTES
              value: service.name=$(OTEL_SERVICE_NAME),service.namespace=opentelemetry-demo,service.version=2.0.2
          resources:
            limits:
              memory: 250Mi
          securityContext:
            runAsGroup: 1001
            runAsNonRoot: true
            runAsUser: 1001
          volumeMounts:
      volumes:
---
# Source: opentelemetry-demo/templates/component.yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: frontend-proxy
  labels:
    
    opentelemetry.io/name: frontend-proxy
    app.kubernetes.io/instance: opentelemetry-demo
    app.kubernetes.io/component: frontend-proxy
    app.kubernetes.io/name: frontend-proxy
    app.kubernetes.io/version: "2.0.2"
    app.kubernetes.io/part-of: opentelemetry-demo
spec:
  replicas: 1
  revisionHistoryLimit: 10
  selector:
    matchLabels:
      
      opentelemetry.io/name: frontend-proxy
  template:
    metadata:
      labels:
        
        opentelemetry.io/name: frontend-proxy
        app.kubernetes.io/instance: opentelemetry-demo
        app.kubernetes.io/component: frontend-proxy
        app.kubernetes.io/name: frontend-proxy
    spec:
      serviceAccountName: opentelemetry-demo
      containers:
        - name: frontend-proxy
          image: 'swr.cn-north-4.myhuaweicloud.com/ddn-k8s/ghcr.io/open-telemetry/demo:2.0.2-frontend-proxy'
          imagePullPolicy: IfNotPresent
          ports:
            
            - containerPort: 8080
              name: service
          env:
            - name: OTEL_SERVICE_NAME
              valueFrom:
                fieldRef:
                  apiVersion: v1
                  fieldPath: metadata.labels['app.kubernetes.io/component']
            - name: OTEL_COLLECTOR_NAME
              value: otel-collector
            - name: OTEL_EXPORTER_OTLP_METRICS_TEMPORALITY_PREFERENCE
              value: cumulative
            - name: ENVOY_PORT
              value: "8080"
            - name: FLAGD_HOST
              value: flagd
            - name: FLAGD_PORT
              value: "8013"
            - name: FLAGD_UI_HOST
              value: flagd
            - name: FLAGD_UI_PORT
              value: "4000"
            - name: FRONTEND_HOST
              value: frontend
            - name: FRONTEND_PORT
              value: "8080"
            - name: GRAFANA_HOST
              value: grafana
            - name: GRAFANA_PORT
              value: "80"
            - name: IMAGE_PROVIDER_HOST
              value: image-provider
            - name: IMAGE_PROVIDER_PORT
              value: "8081"
            - name: JAEGER_HOST
              value: jaeger-query
            - name: JAEGER_PORT
              value: "16686"
            - name: LOCUST_WEB_HOST
              value: load-generator
            - name: LOCUST_WEB_PORT
              value: "8089"
            - name: OTEL_COLLECTOR_HOST
              value: $(OTEL_COLLECTOR_NAME)
            - name: OTEL_COLLECTOR_PORT_GRPC
              value: "4317"
            - name: OTEL_COLLECTOR_PORT_HTTP
              value: "4318"
            - name: OTEL_RESOURCE_ATTRIBUTES
              value: service.name=$(OTEL_SERVICE_NAME),service.namespace=opentelemetry-demo,service.version=2.0.2
          resources:
            limits:
              memory: 65Mi
          securityContext:
            runAsGroup: 101
            runAsNonRoot: true
            runAsUser: 101
          volumeMounts:
      volumes:
---
# Source: opentelemetry-demo/templates/component.yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: image-provider
  labels:
    
    opentelemetry.io/name: image-provider
    app.kubernetes.io/instance: opentelemetry-demo
    app.kubernetes.io/component: image-provider
    app.kubernetes.io/name: image-provider
    app.kubernetes.io/version: "2.0.2"
    app.kubernetes.io/part-of: opentelemetry-demo
spec:
  replicas: 1
  revisionHistoryLimit: 10
  selector:
    matchLabels:
      
      opentelemetry.io/name: image-provider
  template:
    metadata:
      labels:
        
        opentelemetry.io/name: image-provider
        app.kubernetes.io/instance: opentelemetry-demo
        app.kubernetes.io/component: image-provider
        app.kubernetes.io/name: image-provider
    spec:
      serviceAccountName: opentelemetry-demo
      containers:
        - name: image-provider
          image: 'swr.cn-north-4.myhuaweicloud.com/ddn-k8s/ghcr.io/open-telemetry/demo:2.0.2-image-provider'
          imagePullPolicy: IfNotPresent
          ports:
            
            - containerPort: 8081
              name: service
          env:
            - name: OTEL_SERVICE_NAME
              valueFrom:
                fieldRef:
                  apiVersion: v1
                  fieldPath: metadata.labels['app.kubernetes.io/component']
            - name: OTEL_COLLECTOR_NAME
              value: otel-collector
            - name: OTEL_EXPORTER_OTLP_METRICS_TEMPORALITY_PREFERENCE
              value: cumulative
            - name: IMAGE_PROVIDER_PORT
              value: "8081"
            - name: OTEL_COLLECTOR_PORT_GRPC
              value: "4317"
            - name: OTEL_COLLECTOR_HOST
              value: $(OTEL_COLLECTOR_NAME)
            - name: OTEL_RESOURCE_ATTRIBUTES
              value: service.name=$(OTEL_SERVICE_NAME),service.namespace=opentelemetry-demo,service.version=2.0.2
          resources:
            limits:
              memory: 50Mi
          volumeMounts:
      volumes:
---
# Source: opentelemetry-demo/templates/component.yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: kafka
  labels:
    
    opentelemetry.io/name: kafka
    app.kubernetes.io/instance: opentelemetry-demo
    app.kubernetes.io/component: kafka
    app.kubernetes.io/name: kafka
    app.kubernetes.io/version: "2.0.2"
    app.kubernetes.io/part-of: opentelemetry-demo
spec:
  replicas: 1
  revisionHistoryLimit: 10
  selector:
    matchLabels:
      
      opentelemetry.io/name: kafka
  template:
    metadata:
      labels:
        
        opentelemetry.io/name: kafka
        app.kubernetes.io/instance: opentelemetry-demo
        app.kubernetes.io/component: kafka
        app.kubernetes.io/name: kafka
    spec:
      serviceAccountName: opentelemetry-demo
      containers:
        - name: kafka
          image: 'swr.cn-north-4.myhuaweicloud.com/ddn-k8s/ghcr.io/open-telemetry/demo:2.0.2-kafka'
          imagePullPolicy: IfNotPresent
          ports:
            
            - containerPort: 9092
              name: plaintext
            - containerPort: 9093
              name: controller
          env:
            - name: OTEL_SERVICE_NAME
              valueFrom:
                fieldRef:
                  apiVersion: v1
                  fieldPath: metadata.labels['app.kubernetes.io/component']
            - name: OTEL_COLLECTOR_NAME
              value: otel-collector
            - name: OTEL_EXPORTER_OTLP_METRICS_TEMPORALITY_PREFERENCE
              value: cumulative
            - name: KAFKA_ADVERTISED_LISTENERS
              value: PLAINTEXT://kafka:9092
            - name: OTEL_EXPORTER_OTLP_ENDPOINT
              value: http://$(OTEL_COLLECTOR_NAME):4318
            - name: KAFKA_HEAP_OPTS
              value: -Xmx400M -Xms400M
            - name: OTEL_RESOURCE_ATTRIBUTES
              value: service.name=$(OTEL_SERVICE_NAME),service.namespace=opentelemetry-demo,service.version=2.0.2
          resources:
            limits:
              memory: 600Mi
          securityContext:
            runAsGroup: 1000
            runAsNonRoot: true
            runAsUser: 1000
          volumeMounts:
      volumes:
---
# Source: opentelemetry-demo/templates/component.yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: load-generator
  labels:
    
    opentelemetry.io/name: load-generator
    app.kubernetes.io/instance: opentelemetry-demo
    app.kubernetes.io/component: load-generator
    app.kubernetes.io/name: load-generator
    app.kubernetes.io/version: "2.0.2"
    app.kubernetes.io/part-of: opentelemetry-demo
spec:
  replicas: 1
  revisionHistoryLimit: 10
  selector:
    matchLabels:
      
      opentelemetry.io/name: load-generator
  template:
    metadata:
      labels:
        
        opentelemetry.io/name: load-generator
        app.kubernetes.io/instance: opentelemetry-demo
        app.kubernetes.io/component: load-generator
        app.kubernetes.io/name: load-generator
    spec:
      serviceAccountName: opentelemetry-demo
      containers:
        - name: load-generator
          image: 'swr.cn-north-4.myhuaweicloud.com/ddn-k8s/ghcr.io/open-telemetry/demo:2.0.2-load-generator'
          imagePullPolicy: IfNotPresent
          ports:
            
            - containerPort: 8089
              name: service
          env:
            - name: OTEL_SERVICE_NAME
              valueFrom:
                fieldRef:
                  apiVersion: v1
                  fieldPath: metadata.labels['app.kubernetes.io/component']
            - name: OTEL_COLLECTOR_NAME
              value: otel-collector
            - name: OTEL_EXPORTER_OTLP_METRICS_TEMPORALITY_PREFERENCE
              value: cumulative
            - name: LOCUST_WEB_HOST
              value: 0.0.0.0
            - name: LOCUST_WEB_PORT
              value: "8089"
            - name: LOCUST_USERS
              value: "10"
            - name: LOCUST_SPAWN_RATE
              value: "1"
            - name: LOCUST_HOST
              value: http://frontend-proxy:8080
            - name: LOCUST_HEADLESS
              value: "false"
            - name: LOCUST_AUTOSTART
              value: "true"
            - name: LOCUST_BROWSER_TRAFFIC_ENABLED
              value: "true"
            - name: PROTOCOL_BUFFERS_PYTHON_IMPLEMENTATION
              value: python
            - name: FLAGD_HOST
              value: flagd
            - name: FLAGD_OFREP_PORT
              value: "8016"
            - name: OTEL_EXPORTER_OTLP_ENDPOINT
              value: http://$(OTEL_COLLECTOR_NAME):4317
            - name: OTEL_RESOURCE_ATTRIBUTES
              value: service.name=$(OTEL_SERVICE_NAME),service.namespace=opentelemetry-demo,service.version=2.0.2
          resources:
            limits:
              memory: 1500Mi
          volumeMounts:
      volumes:
---
# Source: opentelemetry-demo/templates/component.yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: payment
  labels:
    
    opentelemetry.io/name: payment
    app.kubernetes.io/instance: opentelemetry-demo
    app.kubernetes.io/component: payment
    app.kubernetes.io/name: payment
    app.kubernetes.io/version: "2.0.2"
    app.kubernetes.io/part-of: opentelemetry-demo
spec:
  replicas: 1
  revisionHistoryLimit: 10
  selector:
    matchLabels:
      
      opentelemetry.io/name: payment
  template:
    metadata:
      labels:
        
        opentelemetry.io/name: payment
        app.kubernetes.io/instance: opentelemetry-demo
        app.kubernetes.io/component: payment
        app.kubernetes.io/name: payment
    spec:
      serviceAccountName: opentelemetry-demo
      containers:
        - name: payment
          image: 'swr.cn-north-4.myhuaweicloud.com/ddn-k8s/ghcr.io/open-telemetry/demo:2.0.2-payment'
          imagePullPolicy: IfNotPresent
          ports:
            
            - containerPort: 8080
              name: service
          env:
            - name: OTEL_SERVICE_NAME
              valueFrom:
                fieldRef:
                  apiVersion: v1
                  fieldPath: metadata.labels['app.kubernetes.io/component']
            - name: OTEL_COLLECTOR_NAME
              value: otel-collector
            - name: OTEL_EXPORTER_OTLP_METRICS_TEMPORALITY_PREFERENCE
              value: cumulative
            - name: PAYMENT_PORT
              value: "8080"
            - name: FLAGD_HOST
              value: flagd
            - name: FLAGD_PORT
              value: "8013"
            - name: OTEL_EXPORTER_OTLP_ENDPOINT
              value: http://$(OTEL_COLLECTOR_NAME):4317
            - name: OTEL_RESOURCE_ATTRIBUTES
              value: service.name=$(OTEL_SERVICE_NAME),service.namespace=opentelemetry-demo,service.version=2.0.2
          resources:
            limits:
              memory: 120Mi
          securityContext:
            runAsGroup: 1000
            runAsNonRoot: true
            runAsUser: 1000
          volumeMounts:
      volumes:
---
# Source: opentelemetry-demo/templates/component.yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: product-catalog
  labels:
    
    opentelemetry.io/name: product-catalog
    app.kubernetes.io/instance: opentelemetry-demo
    app.kubernetes.io/component: product-catalog
    app.kubernetes.io/name: product-catalog
    app.kubernetes.io/version: "2.0.2"
    app.kubernetes.io/part-of: opentelemetry-demo
spec:
  replicas: 1
  revisionHistoryLimit: 10
  selector:
    matchLabels:
      
      opentelemetry.io/name: product-catalog
  template:
    metadata:
      labels:
        
        opentelemetry.io/name: product-catalog
        app.kubernetes.io/instance: opentelemetry-demo
        app.kubernetes.io/component: product-catalog
        app.kubernetes.io/name: product-catalog
    spec:
      serviceAccountName: opentelemetry-demo
      containers:
        - name: product-catalog
          image: 'swr.cn-north-4.myhuaweicloud.com/ddn-k8s/ghcr.io/open-telemetry/demo:2.0.2-product-catalog'
          imagePullPolicy: IfNotPresent
          ports:
            
            - containerPort: 8080
              name: service
          env:
            - name: OTEL_SERVICE_NAME
              valueFrom:
                fieldRef:
                  apiVersion: v1
                  fieldPath: metadata.labels['app.kubernetes.io/component']
            - name: OTEL_COLLECTOR_NAME
              value: otel-collector
            - name: OTEL_EXPORTER_OTLP_METRICS_TEMPORALITY_PREFERENCE
              value: cumulative
            - name: PRODUCT_CATALOG_PORT
              value: "8080"
            - name: PRODUCT_CATALOG_RELOAD_INTERVAL
              value: "10"
            - name: FLAGD_HOST
              value: flagd
            - name: FLAGD_PORT
              value: "8013"
            - name: OTEL_EXPORTER_OTLP_ENDPOINT
              value: http://$(OTEL_COLLECTOR_NAME):4317
            - name: OTEL_RESOURCE_ATTRIBUTES
              value: service.name=$(OTEL_SERVICE_NAME),service.namespace=opentelemetry-demo,service.version=2.0.2
          resources:
            limits:
              memory: 20Mi
          volumeMounts:
            - name: product-catalog-products
              mountPath: /usr/src/app/products
      volumes:
        - name: product-catalog-products
          configMap:
            name: product-catalog-products
---
# Source: opentelemetry-demo/templates/component.yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: quote
  labels:
    
    opentelemetry.io/name: quote
    app.kubernetes.io/instance: opentelemetry-demo
    app.kubernetes.io/component: quote
    app.kubernetes.io/name: quote
    app.kubernetes.io/version: "2.0.2"
    app.kubernetes.io/part-of: opentelemetry-demo
spec:
  replicas: 1
  revisionHistoryLimit: 10
  selector:
    matchLabels:
      
      opentelemetry.io/name: quote
  template:
    metadata:
      labels:
        
        opentelemetry.io/name: quote
        app.kubernetes.io/instance: opentelemetry-demo
        app.kubernetes.io/component: quote
        app.kubernetes.io/name: quote
    spec:
      serviceAccountName: opentelemetry-demo
      containers:
        - name: quote
          image: 'swr.cn-north-4.myhuaweicloud.com/ddn-k8s/ghcr.io/open-telemetry/demo:2.0.2-quote'
          imagePullPolicy: IfNotPresent
          ports:
            
            - containerPort: 8080
              name: service
          env:
            - name: OTEL_SERVICE_NAME
              valueFrom:
                fieldRef:
                  apiVersion: v1
                  fieldPath: metadata.labels['app.kubernetes.io/component']
            - name: OTEL_COLLECTOR_NAME
              value: otel-collector
            - name: OTEL_EXPORTER_OTLP_METRICS_TEMPORALITY_PREFERENCE
              value: cumulative
            - name: QUOTE_PORT
              value: "8080"
            - name: OTEL_PHP_AUTOLOAD_ENABLED
              value: "true"
            - name: OTEL_EXPORTER_OTLP_ENDPOINT
              value: http://$(OTEL_COLLECTOR_NAME):4318
            - name: OTEL_RESOURCE_ATTRIBUTES
              value: service.name=$(OTEL_SERVICE_NAME),service.namespace=opentelemetry-demo,service.version=2.0.2
          resources:
            limits:
              memory: 40Mi
          securityContext:
            runAsGroup: 33
            runAsNonRoot: true
            runAsUser: 33
          volumeMounts:
      volumes:
---
# Source: opentelemetry-demo/templates/component.yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: recommendation
  labels:
    
    opentelemetry.io/name: recommendation
    app.kubernetes.io/instance: opentelemetry-demo
    app.kubernetes.io/component: recommendation
    app.kubernetes.io/name: recommendation
    app.kubernetes.io/version: "2.0.2"
    app.kubernetes.io/part-of: opentelemetry-demo
spec:
  replicas: 1
  revisionHistoryLimit: 10
  selector:
    matchLabels:
      
      opentelemetry.io/name: recommendation
  template:
    metadata:
      labels:
        
        opentelemetry.io/name: recommendation
        app.kubernetes.io/instance: opentelemetry-demo
        app.kubernetes.io/component: recommendation
        app.kubernetes.io/name: recommendation
    spec:
      serviceAccountName: opentelemetry-demo
      containers:
        - name: recommendation
          image: 'swr.cn-north-4.myhuaweicloud.com/ddn-k8s/ghcr.io/open-telemetry/demo:2.0.2-recommendation'
          imagePullPolicy: IfNotPresent
          ports:
            
            - containerPort: 8080
              name: service
          env:
            - name: OTEL_SERVICE_NAME
              valueFrom:
                fieldRef:
                  apiVersion: v1
                  fieldPath: metadata.labels['app.kubernetes.io/component']
            - name: OTEL_COLLECTOR_NAME
              value: otel-collector
            - name: OTEL_EXPORTER_OTLP_METRICS_TEMPORALITY_PREFERENCE
              value: cumulative
            - name: RECOMMENDATION_PORT
              value: "8080"
            - name: PRODUCT_CATALOG_ADDR
              value: product-catalog:8080
            - name: OTEL_PYTHON_LOG_CORRELATION
              value: "true"
            - name: PROTOCOL_BUFFERS_PYTHON_IMPLEMENTATION
              value: python
            - name: FLAGD_HOST
              value: flagd
            - name: FLAGD_PORT
              value: "8013"
            - name: OTEL_EXPORTER_OTLP_ENDPOINT
              value: http://$(OTEL_COLLECTOR_NAME):4317
            - name: OTEL_RESOURCE_ATTRIBUTES
              value: service.name=$(OTEL_SERVICE_NAME),service.namespace=opentelemetry-demo,service.version=2.0.2
          resources:
            limits:
              memory: 500Mi
          volumeMounts:
      volumes:
---
# Source: opentelemetry-demo/templates/component.yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: shipping
  labels:
    
    opentelemetry.io/name: shipping
    app.kubernetes.io/instance: opentelemetry-demo
    app.kubernetes.io/component: shipping
    app.kubernetes.io/name: shipping
    app.kubernetes.io/version: "2.0.2"
    app.kubernetes.io/part-of: opentelemetry-demo
spec:
  replicas: 1
  revisionHistoryLimit: 10
  selector:
    matchLabels:
      
      opentelemetry.io/name: shipping
  template:
    metadata:
      labels:
        
        opentelemetry.io/name: shipping
        app.kubernetes.io/instance: opentelemetry-demo
        app.kubernetes.io/component: shipping
        app.kubernetes.io/name: shipping
    spec:
      serviceAccountName: opentelemetry-demo
      containers:
        - name: shipping
          image: 'swr.cn-north-4.myhuaweicloud.com/ddn-k8s/ghcr.io/open-telemetry/demo:2.0.2-shipping'
          imagePullPolicy: IfNotPresent
          ports:
            
            - containerPort: 8080
              name: service
          env:
            - name: OTEL_SERVICE_NAME
              valueFrom:
                fieldRef:
                  apiVersion: v1
                  fieldPath: metadata.labels['app.kubernetes.io/component']
            - name: OTEL_COLLECTOR_NAME
              value: otel-collector
            - name: OTEL_EXPORTER_OTLP_METRICS_TEMPORALITY_PREFERENCE
              value: cumulative
            - name: SHIPPING_PORT
              value: "8080"
            - name: QUOTE_ADDR
              value: http://quote:8080
            - name: OTEL_EXPORTER_OTLP_ENDPOINT
              value: http://$(OTEL_COLLECTOR_NAME):4317
            - name: OTEL_RESOURCE_ATTRIBUTES
              value: service.name=$(OTEL_SERVICE_NAME),service.namespace=opentelemetry-demo,service.version=2.0.2
          resources:
            limits:
              memory: 20Mi
          volumeMounts:
      volumes:
---
# Source: opentelemetry-demo/templates/component.yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: valkey-cart
  labels:
    
    opentelemetry.io/name: valkey-cart
    app.kubernetes.io/instance: opentelemetry-demo
    app.kubernetes.io/component: valkey-cart
    app.kubernetes.io/name: valkey-cart
    app.kubernetes.io/version: "2.0.2"
    app.kubernetes.io/part-of: opentelemetry-demo
spec:
  replicas: 1
  revisionHistoryLimit: 10
  selector:
    matchLabels:
      
      opentelemetry.io/name: valkey-cart
  template:
    metadata:
      labels:
        
        opentelemetry.io/name: valkey-cart
        app.kubernetes.io/instance: opentelemetry-demo
        app.kubernetes.io/component: valkey-cart
        app.kubernetes.io/name: valkey-cart
    spec:
      serviceAccountName: opentelemetry-demo
      containers:
        - name: valkey-cart
          image: 'swr.cn-north-4.myhuaweicloud.com/ddn-k8s/docker.io/valkey/valkey:7.2-alpine'
          imagePullPolicy: IfNotPresent
          ports:
            
            - containerPort: 6379
              name: valkey-cart
          env:
            - name: OTEL_SERVICE_NAME
              valueFrom:
                fieldRef:
                  apiVersion: v1
                  fieldPath: metadata.labels['app.kubernetes.io/component']
            - name: OTEL_COLLECTOR_NAME
              value: otel-collector
            - name: OTEL_EXPORTER_OTLP_METRICS_TEMPORALITY_PREFERENCE
              value: cumulative
            - name: OTEL_RESOURCE_ATTRIBUTES
              value: service.name=$(OTEL_SERVICE_NAME),service.namespace=opentelemetry-demo,service.version=2.0.2
          resources:
            limits:
              memory: 20Mi
          securityContext:
            runAsGroup: 1000
            runAsNonRoot: true
            runAsUser: 999
          volumeMounts:
      volumes:
---
# Source: opentelemetry-demo/charts/opensearch/templates/statefulset.yaml
apiVersion: apps/v1
kind: StatefulSet
metadata:
  name: opensearch
  labels:
    app.kubernetes.io/name: opensearch
    app.kubernetes.io/instance: opentelemetry-demo
    app.kubernetes.io/version: "2.19.0"
    app.kubernetes.io/component: opensearch
  annotations:
    majorVersion: "2"
spec:
  serviceName: opensearch-headless
  selector:
    matchLabels:
      app.kubernetes.io/name: opensearch
      app.kubernetes.io/instance: opentelemetry-demo
  replicas: 1
  podManagementPolicy: Parallel
  updateStrategy:
    type: RollingUpdate
  template:
    metadata:
      name: "opensearch"
      labels:
        app.kubernetes.io/name: opensearch
        app.kubernetes.io/instance: opentelemetry-demo
        app.kubernetes.io/version: "2.19.0"
        app.kubernetes.io/component: opensearch
      annotations:
        configchecksum: 39d5e484f1cc28f685f786a856c4336341c6c034f5c0b1a81337e4a39511e47
    spec:
      securityContext:
        fsGroup: 1000
        runAsUser: 1000
      automountServiceAccountToken: false
      affinity:
        podAntiAffinity:
          preferredDuringSchedulingIgnoredDuringExecution:
          - weight: 1
            podAffinityTerm:
              topologyKey: kubernetes.io/hostname
              labelSelector:
                matchExpressions:
                - key: app.kubernetes.io/instance
                  operator: In
                  values:
                  - opentelemetry-demo
                - key: app.kubernetes.io/name
                  operator: In
                  values:
                  - opensearch
      terminationGracePeriodSeconds: 120
      volumes:
      - name: config
        configMap:
          name: opensearch-config
      - emptyDir: {}
        name: config-emptydir
      enableServiceLinks: true
      initContainers:
      - name: configfile
        image: "swr.cn-north-4.myhuaweicloud.com/ddn-k8s/docker.io/opensearchproject/opensearch:2.19.0"
        imagePullPolicy: "IfNotPresent"
        command:
        - sh
        - -c
        - |
          #!/usr/bin/env bash
          cp -r /tmp/configfolder/*  /tmp/config/
        resources:
          {}
        volumeMounts:
          - mountPath: /tmp/config/
            name: config-emptydir
          - name: config
            mountPath: /tmp/configfolder/opensearch.yml
            subPath: opensearch.yml
      containers:
      - name: "opensearch"
        securityContext:
          capabilities:
            drop:
            - ALL
          runAsNonRoot: true
          runAsUser: 1000

        image: "swr.cn-north-4.myhuaweicloud.com/ddn-k8s/docker.io/opensearchproject/opensearch:2.19.0"
        imagePullPolicy: "IfNotPresent"
        readinessProbe:
          failureThreshold: 3
          periodSeconds: 5
          tcpSocket:
            port: 9200
          timeoutSeconds: 3
        startupProbe:
          failureThreshold: 30
          initialDelaySeconds: 5
          periodSeconds: 10
          tcpSocket:
            port: 9200
          timeoutSeconds: 3
        ports:
        - name: http
          containerPort: 9200
        - name: transport
          containerPort: 9300
        - name: metrics
          containerPort: 9600
        resources:
          limits:
            memory: 1100Mi
          requests:
            cpu: 1000m
            memory: 100Mi
        env:
        - name: node.name
          valueFrom:
            fieldRef:
              fieldPath: metadata.name
        - name: discovery.seed_hosts
          value: "opensearch-cluster-master-headless"
        - name: cluster.name
          value: "demo-cluster"
        - name: network.host
          value: "0.0.0.0"
        - name: OPENSEARCH_JAVA_OPTS
          value: "-Xms300m -Xmx300m"
        - name: node.roles
          value: "master,ingest,data,remote_cluster_client,"
        - name: discovery.type
          value: "single-node"
        - name: bootstrap.memory_lock
          value: "true"
        - name: DISABLE_INSTALL_DEMO_CONFIG
          value: "true"
        - name: DISABLE_SECURITY_PLUGIN
          value: "true"
        volumeMounts:
        - name: config-emptydir
          mountPath: /usr/share/opensearch/config/opensearch.yml
          subPath: opensearch.yml
