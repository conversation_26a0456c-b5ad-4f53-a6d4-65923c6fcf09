# Odoo Monitoring and Error Handling Improvements

import logging
import traceback
from functools import wraps

# Enhanced logging configuration
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('/var/log/odoo/error.log'),
        logging.StreamHandler()
    ]
)

logger = logging.getLogger('odoo.server.fix')

def safe_request_handler(func):
    """
    Decorator to safely handle HTTP requests and prevent crashes
    """
    @wraps(func)
    def wrapper(self, *args, **kwargs):
        try:
            return func(self, *args, **kwargs)
        except AttributeError as e:
            if "'super' object has no attribute 'headers'" in str(e):
                logger.error(f"Werkzeug compatibility error: {e}")
                logger.error(f"Request details: {getattr(self, 'raw_requestline', 'Unknown')}")
                # Send a simple error response without using the problematic method
                try:
                    self.wfile.write(b'HTTP/1.1 400 Bad Request\r\n\r\n')
                    self.wfile.flush()
                except:
                    pass
                return
            else:
                raise
        except Exception as e:
            logger.error(f"Unexpected error in request handler: {e}")
            logger.error(f"Traceback: {traceback.format_exc()}")
            try:
                self.send_error(500, "Internal Server Error")
            except:
                pass
    return wrapper

# Request validation
def validate_http_request(raw_request):
    """
    Validate HTTP request to filter out malicious or malformed requests
    """
    if not raw_request:
        return False
    
    try:
        # Check for control characters (except allowed ones)
        request_str = raw_request.decode('utf-8', errors='ignore')
        
        # Check for suspicious patterns
        suspicious_patterns = [
            '\x00',  # Null bytes
            '\x01', '\x02', '\x03',  # Control characters
            'mstshash',  # Specific pattern from the error
        ]
        
        for pattern in suspicious_patterns:
            if pattern in request_str:
                logger.warning(f"Suspicious request detected: {pattern}")
                return False
                
        return True
        
    except Exception as e:
        logger.error(f"Error validating request: {e}")
        return False

# Rate limiting for suspicious IPs
class RateLimiter:
    def __init__(self):
        self.requests = {}
        self.blocked_ips = set()
    
    def is_allowed(self, ip_address):
        """Check if IP is allowed to make requests"""
        if ip_address in self.blocked_ips:
            return False
        
        # Simple rate limiting logic
        current_time = time.time()
        if ip_address not in self.requests:
            self.requests[ip_address] = []
        
        # Clean old requests
        self.requests[ip_address] = [
            req_time for req_time in self.requests[ip_address]
            if current_time - req_time < 60  # 1 minute window
        ]
        
        # Check rate limit (max 100 requests per minute)
        if len(self.requests[ip_address]) > 100:
            self.blocked_ips.add(ip_address)
            logger.warning(f"IP {ip_address} blocked due to rate limiting")
            return False
        
        self.requests[ip_address].append(current_time)
        return True

# Health check endpoint
def health_check():
    """Simple health check for monitoring"""
    return {
        'status': 'healthy',
        'timestamp': time.time(),
        'version': 'odoo-fixed'
    }

# Configuration recommendations
RECOMMENDED_CONFIG = """
# Add to odoo.conf
[options]
# Limit request size to prevent large malicious requests
limit_request_line = 4096
limit_request_fields = 100
limit_request_field_size = 8190

# Enable proxy mode if behind reverse proxy
proxy_mode = True

# Log configuration
log_level = info
log_handler = :INFO

# Security settings
list_db = False
admin_passwd = your_secure_password_here
"""

print("Odoo monitoring and error handling improvements loaded.")
print("Apply the safe_request_handler decorator to problematic methods.")
print("Use validate_http_request() to filter incoming requests.")
